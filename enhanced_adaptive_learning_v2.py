"""
Enhanced Adaptive Learning System V2
Implements proper validation, tournament level distinction, and prevents overfitting
Addresses all issues identified in the original system
"""

import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass, field
from pathlib import Path
import json
import sqlite3

from adaptive_learning_system import AdaptiveLearningSystem, WeightConfiguration, LearningMetrics
from prediction_tracker import PredictionRecord, PredictionTracker
from robust_validation_system import RobustValidationSystem, CrossValidationConfig
from player_data_quality_validator import ValidationR<PERSON>ult


@dataclass
class TournamentLevelConfig:
    """Configuration for tournament level handling"""
    atp_weight_multiplier: float = 1.0      # Baseline for ATP
    challenger_weight_multiplier: float = 0.8  # Challenger matches are more predictable
    wta_weight_multiplier: float = 1.1      # WTA might need different approach
    
    # Minimum sample sizes by tournament level - Updated with research-based requirements
    atp_min_samples: int = 150        # Up from 40 - Research-based minimum for ATP
    challenger_min_samples: int = 120  # Up from 30 - Research-based minimum for Challenger
    wta_min_samples: int = 140        # Up from 35 - Research-based minimum for WTA
    
    # Learning rates by tournament level
    atp_learning_rate: float = 0.02
    challenger_learning_rate: float = 0.03  # Can learn faster due to more predictable patterns
    wta_learning_rate: float = 0.025


@dataclass
class EnhancedSampleRequirements:
    """Enhanced sample requirements with tournament level consideration"""
    
    # Base requirements by context - Updated with research-based requirements
    base_requirements: Dict[str, int] = field(default_factory=lambda: {
        'ATP_Clay': 260,      # Up from 60 - Research-based: 200 base × 1.3 clay multiplier
        'ATP_Hard': 200,      # Up from 50 - Research-based baseline
        'ATP_Grass': 300,     # Up from 70 - Research-based: 200 base × 1.5 grass multiplier
        'Challenger_Clay': 200, # Up from 45 - Research-based with challenger adjustment
        'Challenger_Hard': 160, # Up from 35 - Research-based with challenger adjustment
        'Challenger_Grass': 240, # Up from 55 - Research-based with challenger adjustment
        'WTA_Clay': 240,      # Up from 55 - Research-based with WTA adjustment
        'WTA_Hard': 180,      # Up from 45 - Research-based with WTA adjustment
        'WTA_Grass': 280      # Up from 65 - Research-based with WTA adjustment
    })
    
    # Quality multipliers
    quality_multipliers: Dict[str, float] = field(default_factory=lambda: {
        'high_quality': 0.8,    # Need fewer samples for high quality data
        'medium_quality': 1.0,  # Standard requirement
        'low_quality': 1.5      # Need more samples for low quality data
    })


class EnhancedAdaptiveLearningSystemV2:
    """
    Enhanced adaptive learning system that addresses all identified issues:
    1. Tournament level distinction
    2. Proper validation methodology
    3. Overfitting prevention
    4. Data quality segmentation
    5. Statistical significance testing
    """
    
    def __init__(self, storage_dir: str = "enhanced_learning_data_v2"):
        self.storage_dir = Path(storage_dir)
        self.storage_dir.mkdir(exist_ok=True)
        
        # Core components
        self.prediction_tracker = PredictionTracker()
        self.validation_system = RobustValidationSystem()
        
        # Configuration
        self.tournament_config = TournamentLevelConfig()
        self.sample_requirements = EnhancedSampleRequirements()
        
        # Storage files
        self.weights_file = self.storage_dir / "enhanced_weight_configurations.json"
        self.validation_history_file = self.storage_dir / "validation_history.json"
        self.learning_log_file = self.storage_dir / "learning_log.json"
        
        # Current state
        self.segment_weights: Dict[str, WeightConfiguration] = {}
        self.validation_history: List[Dict[str, Any]] = []
        self.learning_log: List[Dict[str, Any]] = []
        
        # Load existing data
        self._load_existing_data()
        
        # Validation configuration - Updated with research-based requirements
        self.validation_config = CrossValidationConfig(
            n_splits=5,
            test_size=0.2,
            min_train_size=100,    # Up from 50 - Research-based minimum
            min_test_size=50,      # Up from 15 - Research-based minimum
            bootstrap_samples=1000,
            temporal_validation=True,
            separate_by_tournament=True,
            separate_by_surface=True,
            min_data_quality_score=70.0,
            # Tiered validation thresholds
            basic_validation_threshold=150,
            standard_validation_threshold=400,
            robust_validation_threshold=600,
            high_confidence_threshold=800
        )
    
    def add_prediction_with_tournament_level(self, prediction_record: PredictionRecord,
                                           tournament_level: str = None) -> bool:
        """Add prediction with tournament level classification"""
        
        # Infer tournament level if not provided
        if not tournament_level:
            tournament_level = self._infer_tournament_level(prediction_record)
        
        # Add tournament level to prediction record
        prediction_record.tournament_level = tournament_level
        
        # Add to tracker
        self.prediction_tracker.predictions.append(prediction_record)
        
        # Check if we should trigger learning
        segment_key = self._get_segment_key(prediction_record)
        if self._should_trigger_learning(segment_key):
            self._trigger_segment_learning(segment_key)
        
        return True
    
    def _infer_tournament_level(self, prediction_record: PredictionRecord) -> str:
        """Infer tournament level from available data"""
        # First, check if tournament_level is already set as direct attribute
        if hasattr(prediction_record, 'tournament_level') and prediction_record.tournament_level:
            return prediction_record.tournament_level

        # Check learning_metadata for tournament_level (primary source)
        learning_metadata = getattr(prediction_record, 'learning_metadata', {})
        if learning_metadata and isinstance(learning_metadata, dict):
            tournament_level = learning_metadata.get('tournament_level', None)
            if tournament_level:
                return tournament_level

        # Check other metadata sources
        context_factors = getattr(prediction_record, 'context_factors', {})
        if context_factors and isinstance(context_factors, dict):
            tournament_level = context_factors.get('tournament_level', None)
            if tournament_level:
                return tournament_level

        # Default fallback - could be enhanced with player ranking analysis, etc.
        return 'Mixed'
    
    def _get_segment_key(self, prediction_record: PredictionRecord) -> str:
        """Get segment key for a prediction"""
        # Use the inference method to get tournament level (checks metadata too)
        tournament_level = self._infer_tournament_level(prediction_record)
        surface = prediction_record.surface or 'Unknown'
        return f"{tournament_level}_{surface}"
    
    def _should_trigger_learning(self, segment_key: str) -> bool:
        """Determine if we should trigger learning for a segment"""
        segment_predictions = self._get_segment_predictions(segment_key)
        completed_predictions = [p for p in segment_predictions if p.actual_winner is not None]
        
        # Get minimum sample requirement for this segment
        min_samples = self.sample_requirements.base_requirements.get(segment_key, 50)
        
        # Check if we have enough data
        if len(completed_predictions) < min_samples:
            return False
        
        # Check if enough new data has been added since last learning
        last_learning_count = self._get_last_learning_count(segment_key)
        new_data_threshold = max(200, min_samples // 5)  # At least 200 or 20% of min samples (research-based)
        
        return len(completed_predictions) >= last_learning_count + new_data_threshold
    
    def _get_segment_predictions(self, segment_key: str) -> List[PredictionRecord]:
        """Get all AI predictions for a specific segment"""
        predictions = []
        for pred in self.prediction_tracker.predictions:
            # Only include AI predictions for learning
            if self._get_segment_key(pred) == segment_key and getattr(pred, 'is_ai_prediction', False):
                predictions.append(pred)
        return predictions
    
    def _get_last_learning_count(self, segment_key: str) -> int:
        """Get the number of predictions when learning was last triggered for this segment"""
        for log_entry in reversed(self.learning_log):
            if log_entry.get('segment_key') == segment_key:
                return log_entry.get('prediction_count', 0)
        return 0
    
    def _trigger_segment_learning(self, segment_key: str):
        """Trigger learning for a specific segment using coordinated validation system"""
        print(f"🚀 Starting coordinated validation for segment: {segment_key}")

        # Use the new coordinated learning validation system
        try:
            from enhanced_adaptive_learning_system import run_coordinated_learning_validation

            # Run coordinated validation - this will automatically determine whether to run
            # balance validation (priority 1) or weight validation (priority 2)
            validation_result = run_coordinated_learning_validation()

            if validation_result.get('status') == 'success':
                validation_type = validation_result.get('validation_type', 'unknown')
                print(f"✅ Coordinated validation successful: {validation_type}")

                if validation_type == 'balance_validation':
                    print("   🎯 Balance ratios optimized (Historical vs Momentum)")
                elif validation_type == 'weight_validation':
                    print("   ⚙️ Momentum factor weights optimized")

            elif validation_result.get('status') == 'blocked':
                print(f"🚫 Validation blocked by coordination system:")
                print(f"   Balance validation: {validation_result.get('balance_reason', 'Unknown')}")
                print(f"   Weight validation: {validation_result.get('weight_reason', 'Unknown')}")
                print(f"   Recommendation: {validation_result.get('recommendation', 'Try again later')}")

            elif validation_result.get('status') == 'insufficient_data':
                sample_size = validation_result.get('sample_size', 0)
                required_size = validation_result.get('required_size', 0)
                validation_type = validation_result.get('validation_type', 'unknown')
                print(f"⚠️ Insufficient data for {validation_type}: {sample_size}/{required_size} predictions")

            else:
                print(f"❌ Validation failed: {validation_result.get('status', 'unknown')}")

            return validation_result

        except ImportError as e:
            print(f"⚠️ Coordinated learning system not available: {e}")
            print("   Falling back to legacy weight validation...")
            return self._legacy_segment_learning(segment_key)

    def _legacy_segment_learning(self, segment_key: str):
        """Legacy segment learning method (fallback when coordinated system unavailable)"""
        print(f"Triggering legacy learning for segment: {segment_key}")

        segment_predictions = self._get_segment_predictions(segment_key)
        completed_predictions = [p for p in segment_predictions if p.actual_winner is not None]

        # Only use predictions from completed matches for learning
        learning_eligible_predictions = [p for p in completed_predictions
                                       if self._is_prediction_eligible_for_learning(p)]

        if len(learning_eligible_predictions) < len(completed_predictions):
            print(f"⏸️ Filtered {len(completed_predictions) - len(learning_eligible_predictions)} predictions from pending matches")

        completed_predictions = learning_eligible_predictions

        # Perform robust validation
        validation_result = self.validation_system.validate_prediction_system(completed_predictions)

        # Log the validation attempt
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'segment_key': segment_key,
            'prediction_count': len(completed_predictions),
            'validation_result': validation_result,
            'action_taken': 'none',
            'method': 'legacy_fallback'
        }

        # Check if validation shows statistical significance
        if self._is_validation_successful(validation_result):
            # Extract optimal weights from validation
            optimal_weights = self._extract_optimal_weights(validation_result)

            if optimal_weights:
                # Update segment weights
                self.segment_weights[segment_key] = optimal_weights
                log_entry['action_taken'] = 'weights_updated'
                log_entry['new_weights'] = optimal_weights.to_dict()

                print(f"Updated weights for segment {segment_key}")
            else:
                log_entry['action_taken'] = 'no_improvement_found'
        else:
            log_entry['action_taken'] = 'validation_failed'
            print(f"Validation failed for segment {segment_key} - not updating weights")

        # Save log entry
        self.learning_log.append(log_entry)
        self._save_learning_log()

        # Save validation history
        self.validation_history.append({
            'timestamp': datetime.now().isoformat(),
            'segment_key': segment_key,
            'result': validation_result
        })
        self._save_validation_history()

        return {
            'status': 'legacy_completed',
            'validation_result': validation_result,
            'action_taken': log_entry['action_taken']
        }

        # Notify coordinator of completion
        try:
            from learning_system_coordinator import learning_coordinator
            success = log_entry['action_taken'] == 'weights_updated'
            learning_coordinator.complete_operation(operation, success, {
                'segment_key': segment_key,
                'action_taken': log_entry['action_taken'],
                'prediction_count': len(completed_predictions)
            })
        except (ImportError, NameError):
            pass
    
    def _is_validation_successful(self, validation_result: Dict[str, Any]) -> bool:
        """Check if validation result indicates successful learning"""
        if validation_result.get('status') != 'success':
            return False
        
        overall_summary = validation_result.get('overall_summary', {})
        reliability_score = overall_summary.get('system_reliability_score', 0.0)
        overall_accuracy = overall_summary.get('overall_accuracy', 0.0)
        
        # Require both good reliability and accuracy
        return reliability_score >= 0.6 and overall_accuracy >= 0.55
    
    def _extract_optimal_weights(self, validation_result: Dict[str, Any]) -> Optional[WeightConfiguration]:
        """Extract optimal weights from validation result"""
        # This is a simplified extraction - in practice you'd analyze all segments
        # and find the best performing weight configuration
        
        segment_results = validation_result.get('segment_results', {})
        best_accuracy = 0.0
        best_weights = None
        
        for segment_key, segment_data in segment_results.items():
            bootstrap_data = segment_data.get('bootstrap_validation', {})
            if bootstrap_data.get('is_statistically_significant', False):
                accuracy = bootstrap_data.get('mean_accuracy', 0.0)
                if accuracy > best_accuracy:
                    best_accuracy = accuracy
                    # Extract weights from cross-validation results
                    cv_results = segment_data.get('cross_validation', {})
                    # This is simplified - you'd need to track weights through the validation process
                    best_weights = self._create_weight_config_from_accuracy(accuracy)
        
        return best_weights
    
    def _create_weight_config_from_accuracy(self, accuracy: float) -> WeightConfiguration:
        """Create weight configuration based on accuracy (placeholder)"""
        # This is a simplified approach - in practice you'd track the actual weights
        # that produced the best results during validation
        
        base_config = WeightConfiguration()
        base_config.accuracy_score = accuracy
        base_config.sample_size = 50  # Placeholder
        
        return base_config
    
    def get_weights_for_prediction(self, prediction_context: Dict[str, Any]) -> WeightConfiguration:
        """Get optimal weights for a prediction context"""
        
        # Determine segment
        tournament_level = prediction_context.get('tournament_level', 'Mixed')
        surface = prediction_context.get('surface', 'Hard')
        segment_key = f"{tournament_level}_{surface}"
        
        # Check if we have learned weights for this segment
        if segment_key in self.segment_weights:
            learned_weights = self.segment_weights[segment_key]
            
            # Apply tournament-specific adjustments
            adjusted_weights = self._apply_tournament_adjustments(learned_weights, tournament_level)
            return adjusted_weights
        
        # Fallback to default weights with tournament adjustments
        default_weights = WeightConfiguration()
        return self._apply_tournament_adjustments(default_weights, tournament_level)
    
    def _apply_tournament_adjustments(self, weights: WeightConfiguration, 
                                    tournament_level: str) -> WeightConfiguration:
        """Apply tournament-specific weight adjustments"""
        
        # Get multiplier for tournament level
        multiplier = 1.0
        if tournament_level == 'ATP':
            multiplier = self.tournament_config.atp_weight_multiplier
        elif tournament_level == 'Challenger':
            multiplier = self.tournament_config.challenger_weight_multiplier
        elif tournament_level == 'WTA':
            multiplier = self.tournament_config.wta_weight_multiplier
        
        # Create adjusted weights
        adjusted = WeightConfiguration()
        
        # Apply multiplier to momentum-related weights (these vary more by tournament level)
        adjusted.momentum_intensity_weight = weights.momentum_intensity_weight * multiplier
        adjusted.mental_fatigue_weight = weights.mental_fatigue_weight * multiplier
        adjusted.clutch_performance_weight = weights.clutch_performance_weight * multiplier
        
        # Keep service-related weights more stable
        adjusted.service_consistency_weight = weights.service_consistency_weight
        adjusted.service_pressure_weight = weights.service_pressure_weight
        adjusted.current_hold_streak_weight = weights.current_hold_streak_weight
        adjusted.deuce_game_performance_weight = weights.deuce_game_performance_weight
        
        # Normalize to sum to 1.0
        total = (adjusted.momentum_intensity_weight + adjusted.mental_fatigue_weight + 
                adjusted.service_pressure_weight + adjusted.clutch_performance_weight +
                adjusted.service_consistency_weight + adjusted.current_hold_streak_weight +
                adjusted.deuce_game_performance_weight)
        
        if total > 0:
            adjusted.momentum_intensity_weight /= total
            adjusted.mental_fatigue_weight /= total
            adjusted.service_pressure_weight /= total
            adjusted.clutch_performance_weight /= total
            adjusted.service_consistency_weight /= total
            adjusted.current_hold_streak_weight /= total
            adjusted.deuce_game_performance_weight /= total
        
        return adjusted
    
    def get_learning_status(self) -> Dict[str, Any]:
        """Get current learning status for all segments"""
        # Count only AI predictions for learning system
        all_predictions = self.prediction_tracker.predictions
        ai_predictions = [p for p in all_predictions if getattr(p, 'is_ai_prediction', False)]

        status = {
            'segments': {},
            'overall_stats': {
                'total_segments': 0,
                'segments_with_learned_weights': 0,
                'total_predictions': len(ai_predictions),  # Only count AI predictions
                'total_all_predictions': len(all_predictions),  # For debugging
                'last_learning_update': None
            }
        }
        
        # Analyze each segment (only AI predictions)
        segments = set()
        for pred in ai_predictions:  # Only consider AI predictions
            segments.add(self._get_segment_key(pred))

        for segment_key in segments:
            segment_predictions = self._get_segment_predictions(segment_key)
            # Filter for AI predictions only
            ai_segment_predictions = [p for p in segment_predictions if getattr(p, 'is_ai_prediction', False)]
            completed_predictions = [p for p in ai_segment_predictions if p.actual_winner is not None]

            min_required = self.sample_requirements.base_requirements.get(segment_key, 50)
            has_learned_weights = segment_key in self.segment_weights

            status['segments'][segment_key] = {
                'total_predictions': len(ai_segment_predictions),  # Only AI predictions
                'completed_predictions': len(completed_predictions),
                'min_required_for_learning': min_required,
                'ready_for_learning': len(completed_predictions) >= min_required,
                'has_learned_weights': has_learned_weights,
                'last_learning_count': self._get_last_learning_count(segment_key)
            }
            
            if has_learned_weights:
                status['overall_stats']['segments_with_learned_weights'] += 1
        
        status['overall_stats']['total_segments'] = len(segments)
        
        # Get last learning update
        if self.learning_log:
            status['overall_stats']['last_learning_update'] = self.learning_log[-1]['timestamp']
        
        return status

    def reset_learning_data(self, segment_key: str = None):
        """Reset learning data for a specific segment or all segments"""
        if segment_key:
            # Reset specific segment
            if segment_key in self.segment_weights:
                del self.segment_weights[segment_key]

            # Remove from learning log
            self.learning_log = [log for log in self.learning_log
                               if log.get('segment_key') != segment_key]

            print(f"Reset learning data for segment: {segment_key}")
        else:
            # Reset all learning data
            self.segment_weights.clear()
            self.learning_log.clear()
            self.validation_history.clear()

            print("Reset all learning data")

        # Save changes
        self._save_all_data()

    def force_learning_for_segment(self, segment_key: str) -> Dict[str, Any]:
        """Force learning for a specific segment (for testing purposes)"""
        segment_predictions = self._get_segment_predictions(segment_key)
        completed_predictions = [p for p in segment_predictions if p.actual_winner is not None]

        if len(completed_predictions) < 10:
            return {
                'status': 'insufficient_data',
                'completed_predictions': len(completed_predictions),
                'minimum_required': 10
            }

        print(f"Force triggering learning for segment: {segment_key}")
        self._trigger_segment_learning(segment_key)

        return {
            'status': 'learning_triggered',
            'segment_key': segment_key,
            'predictions_used': len(completed_predictions)
        }

    def _load_existing_data(self):
        """Load existing learning data"""
        try:
            # Load weights
            if self.weights_file.exists():
                with open(self.weights_file, 'r') as f:
                    weights_data = json.load(f)
                    for segment_key, weight_dict in weights_data.items():
                        self.segment_weights[segment_key] = WeightConfiguration.from_dict(weight_dict)

            # Load validation history
            if self.validation_history_file.exists():
                with open(self.validation_history_file, 'r') as f:
                    self.validation_history = json.load(f)

            # Load learning log
            if self.learning_log_file.exists():
                with open(self.learning_log_file, 'r') as f:
                    self.learning_log = json.load(f)

        except Exception as e:
            print(f"Error loading existing data: {e}")
            # Initialize empty data structures
            self.segment_weights = {}
            self.validation_history = []
            self.learning_log = []

    def _save_all_data(self):
        """Save all learning data"""
        self._save_weights()
        self._save_validation_history()
        self._save_learning_log()

    def _save_weights(self):
        """Save segment weights"""
        try:
            weights_data = {}
            for segment_key, weights in self.segment_weights.items():
                weights_data[segment_key] = weights.to_dict()

            with open(self.weights_file, 'w') as f:
                json.dump(weights_data, f, indent=2)
        except Exception as e:
            print(f"Error saving weights: {e}")

    def _save_validation_history(self):
        """Save validation history"""
        try:
            with open(self.validation_history_file, 'w') as f:
                json.dump(self.validation_history, f, indent=2, default=str)
        except Exception as e:
            print(f"Error saving validation history: {e}")

    def _save_learning_log(self):
        """Save learning log"""
        try:
            with open(self.learning_log_file, 'w') as f:
                json.dump(self.learning_log, f, indent=2, default=str)
        except Exception as e:
            print(f"Error saving learning log: {e}")

    def export_learning_report(self) -> Dict[str, Any]:
        """Export comprehensive learning report"""
        report = {
            'export_timestamp': datetime.now().isoformat(),
            'system_version': 'EnhancedAdaptiveLearningSystemV2',
            'configuration': {
                'tournament_config': {
                    'atp_min_samples': self.tournament_config.atp_min_samples,
                    'challenger_min_samples': self.tournament_config.challenger_min_samples,
                    'wta_min_samples': self.tournament_config.wta_min_samples
                },
                'validation_config': {
                    'n_splits': self.validation_config.n_splits,
                    'test_size': self.validation_config.test_size,
                    'bootstrap_samples': self.validation_config.bootstrap_samples,
                    'temporal_validation': self.validation_config.temporal_validation
                }
            },
            'learning_status': self.get_learning_status(),
            'segment_weights': {},
            'recent_validations': self.validation_history[-10:] if self.validation_history else [],
            'recent_learning_log': self.learning_log[-20:] if self.learning_log else []
        }

        # Export current weights
        for segment_key, weights in self.segment_weights.items():
            report['segment_weights'][segment_key] = weights.to_dict()

        return report

    def _is_prediction_eligible_for_learning(self, prediction_record: PredictionRecord) -> bool:
        """Check if a prediction is eligible for learning (completed match with outcome)"""
        if not prediction_record.actual_winner:
            return False

        # Only allow learning from completed matches
        if hasattr(prediction_record, 'match_status') and prediction_record.match_status:
            if prediction_record.match_status in ["pending", "draft"]:
                return False

            # Must be from a completed match
            if prediction_record.match_status != "completed":
                return False
        else:
            # If no match status is set, assume it's from an older prediction and allow learning
            # This maintains backward compatibility
            pass

        return True

    def delete_prediction_by_criteria(self, score: tuple, set_number: int, timestamp_str: str, tolerance_seconds: int = 60) -> int:
        """Delete predictions matching the given criteria from the prediction tracker"""
        deleted_count = 0
        target_timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))

        # Find matching predictions
        predictions_to_remove = []
        for pred in self.prediction_tracker.predictions:
            if (pred.score == score and
                pred.set_number == set_number):

                # Check timestamp within tolerance
                pred_timestamp = datetime.fromisoformat(pred.timestamp.replace('Z', '+00:00'))
                time_diff = abs((pred_timestamp - target_timestamp).total_seconds())

                if time_diff <= tolerance_seconds:
                    predictions_to_remove.append(pred)

        # Remove from tracker
        for pred in predictions_to_remove:
            self.prediction_tracker.predictions.remove(pred)
            deleted_count += 1
            print(f"   Removed from enhanced learning v2: {pred.score} at {pred.timestamp[:19]}")

        # Save updated predictions
        if deleted_count > 0:
            self.prediction_tracker.save_data()

        return deleted_count


# Global enhanced learning system instance
enhanced_learning_system_v2 = EnhancedAdaptiveLearningSystemV2()
