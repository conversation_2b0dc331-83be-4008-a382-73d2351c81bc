import sys
from datetime import datetime
from PyQt5.QtWidgets import (QA<PERSON><PERSON>, QMainWindow, QW<PERSON>t, QVBoxLayout,
                           QHBoxLayout, QGridLayout, QLabel, QLineEdit, QPlainTextEdit,
                           QPushButton, QScrollArea, QDialog, QComboBox,
                           QDialogButtonBox, QFormLayout, QTabWidget,
                           QTableWidget, QTableWidgetItem, QHeaderView,
                           QGroupBox, QProgressBar, QTextEdit, QFileDialog,
                           QMessageBox, QCheckBox, QSpinBox, QSizePolicy)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont, QColor, QKeySequence
from PyQt5.QtWidgets import QDesktopWidget, QShortcut
import json
import sqlite3
from enhanced_predictor import EnhancedTennisPredictor, MomentumIndicator
from prediction_tracker import PredictionT<PERSON>, PredictionRecord
from match_session import MatchManager, MatchSession
from gemini_api import gemini_analyzer
from enhanced_gemini_integration import enhanced_gemini_analyzer
from config import config
from player_data_uploader import player_uploader
from automatic_player_downloader import enhanced_player_validation
from improved_player_downloader import show_improved_download_dialog
from automatic_download_system import automatic_player_download
from money_making_betting_system import MoneyMakingBettingSystem

# Import enhanced learning system
try:
    from learning_system_integration import enhanced_gui_integration, learning_integrator
    ENHANCED_LEARNING_AVAILABLE = True
    print("✓ Enhanced Learning System loaded successfully")
except ImportError as e:
    ENHANCED_LEARNING_AVAILABLE = False
    print(f"⚠ Enhanced Learning System not available: {e}")

# Import enhanced validation system
try:
    from player_data_quality_validator import data_quality_validator
    from enhanced_player_download_system import enhanced_download_manager
    ENHANCED_VALIDATION_AVAILABLE = True
except ImportError:
    print("Warning: Enhanced validation system not available")
    ENHANCED_VALIDATION_AVAILABLE = False

# Import sync hooks to keep learning systems synchronized
try:
    import learning_system_sync_hooks
    print("✓ Learning system sync hooks installed")
except ImportError as e:
    print(f"⚠ Learning system sync hooks not available: {e}")

# Dark Mode Color Constants
PRIMARY_BG = "#09090B"           # Very dark gray/black for backgrounds
PRIMARY_INTERACTIVE = "#7054DD"  # Purple for buttons and main interactive elements
SECONDARY_INTERACTIVE = "#8463FF" # Lighter purple for highlights and secondary elements
PRIMARY_TEXT = "#FFFFFF"         # White for all text and labels
BORDER_COLOR = "#7054DD"         # Purple for borders and dividers
SUCCESS_COLOR = "#8463FF"        # Lighter purple for success states
WARNING_COLOR = "#7054DD"        # Main purple for warning states
ERROR_COLOR = "#FF4444"          # Dark mode compatible red for errors

class EnhancedTennisApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Enhanced Tennis Probability Calculator")

        # Get screen geometry and set appropriate window size
        self.setup_window_geometry()

        # Set Inter font as the default application font
        self.setup_inter_font()

        self.tracker = PredictionTracker()
        self.predictor = EnhancedTennisPredictor(self.tracker)
        self.current_prediction = None
        self.outcome_just_recorded = False  # Track if we just recorded an outcome
        self.last_recorded_score = None  # Track the score for which we last recorded an outcome
        self.current_ai_prediction = None  # Track current AI prediction data
        self.auto_analysis_mode = False  # Track if we're in automatic analysis mode (no predictions should be created)

        # Initialize match manager
        self.match_manager = MatchManager()
        self.current_session_id = None

        # Monitor integration
        self._last_processed_timestamp = None
        self._from_monitor = False

        # Initialize betting system
        try:
            self.betting_system = MoneyMakingBettingSystem(starting_bankroll=1000)
            self.betting_system.load_betting_data()  # Load previous betting data if exists
            print("✅ Betting system initialized successfully")
        except Exception as e:
            print(f"⚠️ Warning: Betting system initialization failed: {e}")
            self.betting_system = None

        # Setup automatic monitor checking (with persistence)
        self._load_monitor_state()
        self.monitor_enabled = True  # Can be configured to disable monitor checking
        if self.monitor_enabled:
            self.monitor_check_timer = QTimer()
            self.monitor_check_timer.timeout.connect(self.check_for_monitor_data)
            self.monitor_check_timer.start(5000)  # Check every 5 seconds

        self.setup_ui()

        # Auto-load last session if enabled
        self.auto_load_last_session()

    def setup_window_geometry(self):
        """Set up window geometry based on user preference"""
        # Get the screen geometry
        desktop = QDesktopWidget()
        screen_geometry = desktop.screenGeometry()
        screen_width = screen_geometry.width()
        screen_height = screen_geometry.height()

        print(f"Screen resolution: {screen_width}x{screen_height}")

        # Check user preference for fullscreen
        launch_fullscreen = config.get('launch_fullscreen', True)

        if launch_fullscreen:
            print(f"Launching in fullscreen mode")
            # Set the window to fullscreen
            self.showMaximized()  # This will maximize the window to fill the screen
            self.is_fullscreen_mode = True
        else:
            print(f"Launching in windowed mode")
            # Calculate appropriate window size (80% of screen size, with limits)
            window_width = min(1400, int(screen_width * 0.8))
            window_height = min(900, int(screen_height * 0.8))
            window_width = max(1000, window_width)
            window_height = max(700, window_height)

            # Center the window on screen
            x = (screen_width - window_width) // 2
            y = (screen_height - window_height) // 2

            # Set the geometry
            self.setGeometry(x, y, window_width, window_height)
            self.is_fullscreen_mode = False
            print(f"Window size: {window_width}x{window_height} at position ({x}, {y})")

        # Set reasonable minimum size in case user wants to restore window
        self.setMinimumSize(1000, 700)

        # Add keyboard shortcut for toggling fullscreen (F11)
        self.fullscreen_shortcut = QShortcut(QKeySequence("F11"), self)
        self.fullscreen_shortcut.activated.connect(self.toggle_fullscreen)

    def toggle_fullscreen(self):
        """Toggle between fullscreen and windowed mode (F11)"""
        if self.is_fullscreen_mode:
            # Switch to windowed mode
            self.showNormal()

            # Set a reasonable windowed size
            desktop = QDesktopWidget()
            screen_geometry = desktop.screenGeometry()
            screen_width = screen_geometry.width()
            screen_height = screen_geometry.height()

            # Use 80% of screen size for windowed mode
            window_width = min(1400, int(screen_width * 0.8))
            window_height = min(900, int(screen_height * 0.8))
            window_width = max(1000, window_width)
            window_height = max(700, window_height)

            # Center the window
            x = (screen_width - window_width) // 2
            y = (screen_height - window_height) // 2
            self.setGeometry(x, y, window_width, window_height)

            self.is_fullscreen_mode = False
            print("Switched to windowed mode")
        else:
            # Switch to fullscreen mode
            self.showMaximized()
            self.is_fullscreen_mode = True
            print("Switched to fullscreen mode")

    def setup_inter_font(self):
        """Set up Inter font for the entire application"""
        # Try to set Inter font, fallback to system default if not available
        inter_font = QFont("Inter", 10)
        inter_font.setStyleHint(QFont.SansSerif)

        # Set as application default font
        QApplication.instance().setFont(inter_font)

        # Also set for this window specifically
        self.setFont(inter_font)

    def setup_ui(self):
        # Create menu bar
        self.create_menu_bar()

        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Set comprehensive dark mode styling for entire application
        self.setStyleSheet(f"""
            QMainWindow {{
                background-color: {PRIMARY_BG};
                color: {PRIMARY_TEXT};
            }}
            QWidget {{
                background-color: {PRIMARY_BG};
                color: {PRIMARY_TEXT};
                font-family: 'Inter', sans-serif;
            }}

            /* Labels */
            QLabel {{
                color: {PRIMARY_TEXT};
                font-family: 'Inter', sans-serif;
                font-size: 16px;
            }}

            /* Menu Bar and Menu Styling */
            QMenuBar {{
                background-color: {PRIMARY_BG};
                color: {PRIMARY_TEXT};
                border: none;
                padding: 4px;
                font-size: 11px;
            }}
            QMenuBar::item {{
                background-color: transparent;
                color: {PRIMARY_TEXT};
                padding: 6px 12px;
                border-radius: 4px;
            }}
            QMenuBar::item:selected {{
                background-color: {PRIMARY_INTERACTIVE};
                color: {PRIMARY_TEXT};
            }}
            QMenuBar::item:pressed {{
                background-color: {SECONDARY_INTERACTIVE};
            }}
            QMenu {{
                background-color: {PRIMARY_BG};
                color: {PRIMARY_TEXT};
                border: 1px solid {BORDER_COLOR};
                border-radius: 6px;
                padding: 4px;
                font-size: 11px;
            }}
            QMenu::item {{
                background-color: transparent;
                color: {PRIMARY_TEXT};
                padding: 8px 16px;
                border-radius: 4px;
            }}
            QMenu::item:selected {{
                background-color: {PRIMARY_INTERACTIVE};
                color: {PRIMARY_TEXT};
            }}
            QMenu::separator {{
                height: 1px;
                background-color: {BORDER_COLOR};
                margin: 4px 8px;
            }}

            /* Tab Widget Styling */
            QTabWidget::pane {{
                border: 1px solid {BORDER_COLOR};
                background-color: {PRIMARY_BG};
                border-radius: 6px;
            }}
            QTabBar::tab {{
                background-color: {PRIMARY_BG};
                color: {PRIMARY_TEXT};
                border: 1px solid {BORDER_COLOR};
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
                font-size: 13px;
                font-family: 'Inter', sans-serif;
                min-width: 80px;
            }}
            QTabBar::tab:selected {{
                background-color: {PRIMARY_INTERACTIVE};
                color: {PRIMARY_TEXT};
                border-bottom: 1px solid {PRIMARY_INTERACTIVE};
            }}
            QTabBar::tab:hover:!selected {{
                background-color: {SECONDARY_INTERACTIVE};
                color: {PRIMARY_TEXT};
            }}

            /* Input Fields */
            QLineEdit {{
                background-color: {PRIMARY_BG};
                border: 1px solid {BORDER_COLOR};
                border-radius: 5px;
                padding: 8px;
                color: {PRIMARY_TEXT};
                font-size: 16px;
                font-family: 'Inter', sans-serif;
            }}
            QLineEdit:focus {{
                border: 2px solid {SECONDARY_INTERACTIVE};
                background-color: {PRIMARY_BG};
            }}
            QLineEdit:hover {{
                border-color: {SECONDARY_INTERACTIVE};
            }}

            /* Text Areas */
            QTextEdit, QPlainTextEdit {{
                background-color: {PRIMARY_BG};
                border: 1px solid {BORDER_COLOR};
                border-radius: 5px;
                padding: 8px;
                color: {PRIMARY_TEXT};
                font-size: 16px;
                font-family: 'Inter', sans-serif;
            }}
            QTextEdit:focus, QPlainTextEdit:focus {{
                border: 2px solid {SECONDARY_INTERACTIVE};
            }}
            QTextEdit:hover, QPlainTextEdit:hover {{
                border-color: {SECONDARY_INTERACTIVE};
            }}

            /* Dropdown Menus */
            QComboBox {{
                background-color: {PRIMARY_BG};
                border: 1px solid {BORDER_COLOR};
                border-radius: 5px;
                padding: 8px;
                color: {PRIMARY_TEXT};
                font-size: 16px;
                font-family: 'Inter', sans-serif;
                min-height: 20px;
            }}
            QComboBox:hover {{
                border: 2px solid {SECONDARY_INTERACTIVE};
                background-color: {PRIMARY_BG};
            }}
            QComboBox:focus {{
                border: 2px solid {SECONDARY_INTERACTIVE};
            }}
            QComboBox::drop-down {{
                border: none;
                background-color: {PRIMARY_INTERACTIVE};
                border-radius: 3px;
                width: 20px;
            }}
            QComboBox::down-arrow {{
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid {PRIMARY_TEXT};
                margin: 5px;
            }}
            QComboBox QAbstractItemView {{
                background-color: {PRIMARY_BG};
                border: 2px solid {BORDER_COLOR};
                selection-background-color: {SECONDARY_INTERACTIVE};
                selection-color: {PRIMARY_TEXT};
                color: {PRIMARY_TEXT};
                outline: none;
            }}
            QComboBox QAbstractItemView::item {{
                background-color: {PRIMARY_BG};
                color: {PRIMARY_TEXT};
                padding: 8px;
                border: none;
            }}
            QComboBox QAbstractItemView::item:selected {{
                background-color: {SECONDARY_INTERACTIVE};
                color: {PRIMARY_TEXT};
            }}
            QComboBox QAbstractItemView::item:hover {{
                background-color: {SECONDARY_INTERACTIVE};
                color: {PRIMARY_TEXT};
            }}

            /* Checkboxes */
            QCheckBox {{
                color: {PRIMARY_TEXT};
                font-size: 14px;
                font-family: 'Inter', sans-serif;
                spacing: 8px;
            }}
            QCheckBox::indicator {{
                width: 18px;
                height: 18px;
                border: 2px solid #666666;
                border-radius: 3px;
                background-color: {PRIMARY_BG};
            }}
            QCheckBox::indicator:hover {{
                border-color: #888888;
                background-color: {PRIMARY_BG};
            }}
            QCheckBox::indicator:checked {{
                background-color: #4CAF50;
                border-color: #4CAF50;
                image: none;
            }}
            QCheckBox::indicator:checked:hover {{
                background-color: #66BB6A;
                border-color: #66BB6A;
            }}

            /* Simplified checkbox styling - green background will indicate selection */
            QCheckBox::indicator:checked {{
                background-color: #4CAF50;
                border-color: #4CAF50;
                border-width: 3px;
                border-style: solid;
            }}
            QCheckBox::indicator:checked:hover {{
                background-color: #66BB6A;
                border-color: #66BB6A;
                border-width: 3px;
            }}
            QCheckBox::indicator:unchecked {{
                background-color: {PRIMARY_BG};
                border-color: #666666;
                border-width: 2px;
                border-style: solid;
            }}

            /* Spin Boxes */
            QSpinBox {{
                background-color: {PRIMARY_BG};
                border: 2px solid {BORDER_COLOR};
                border-radius: 5px;
                padding: 5px;
                color: {PRIMARY_TEXT};
                font-size: 13px;
                font-family: 'Inter', sans-serif;
            }}
            QSpinBox:focus {{
                border-color: {SECONDARY_INTERACTIVE};
            }}
            QSpinBox::up-button, QSpinBox::down-button {{
                background-color: {PRIMARY_INTERACTIVE};
                border: none;
                border-radius: 3px;
                width: 16px;
            }}
            QSpinBox::up-button:hover, QSpinBox::down-button:hover {{
                background-color: {SECONDARY_INTERACTIVE};
            }}

            /* Labels */
            QLabel {{
                color: {PRIMARY_TEXT};
                background-color: transparent;
            }}

            /* Tabs */
            QTabWidget::pane {{
                border: 1px solid {BORDER_COLOR};
                background-color: {PRIMARY_BG};
                border-radius: 5px;
            }}
            QTabBar::tab {{
                background-color: {PRIMARY_BG};
                border: 1px solid {BORDER_COLOR};
                border-bottom: none;
                border-radius: 8px 8px 0 0;
                padding: 10px 20px;
                margin-right: 2px;
                color: {PRIMARY_TEXT};
                font-weight: bold;
            }}
            QTabBar::tab:selected {{
                background-color: {PRIMARY_INTERACTIVE};
                color: {PRIMARY_TEXT};
                border-color: {PRIMARY_INTERACTIVE};
            }}
            QTabBar::tab:hover:!selected {{
                background-color: {SECONDARY_INTERACTIVE};
                color: {PRIMARY_TEXT};
            }}
            QTabBar::tab:pressed {{
                background-color: {PRIMARY_INTERACTIVE};
            }}

            /* Scroll Areas */
            QScrollArea {{
                background-color: {PRIMARY_BG};
                border: 1px solid {BORDER_COLOR};
            }}
            QScrollBar:vertical {{
                background-color: {PRIMARY_BG};
                width: 12px;
                border: 1px solid {BORDER_COLOR};
                border-radius: 6px;
            }}
            QScrollBar::handle:vertical {{
                background-color: {PRIMARY_INTERACTIVE};
                border-radius: 5px;
                min-height: 20px;
            }}
            QScrollBar::handle:vertical:hover {{
                background-color: {SECONDARY_INTERACTIVE};
            }}
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
                border: none;
                background: none;
            }}

            /* Menu Bar */
            QMenuBar {{
                background-color: {PRIMARY_BG};
                color: {PRIMARY_TEXT};
                border-bottom: 1px solid {BORDER_COLOR};
            }}
            QMenuBar::item {{
                background-color: transparent;
                padding: 8px 12px;
                color: {PRIMARY_TEXT};
            }}
            QMenuBar::item:selected {{
                background-color: {PRIMARY_INTERACTIVE};
                color: {PRIMARY_TEXT};
            }}
            QMenuBar::item:pressed {{
                background-color: {SECONDARY_INTERACTIVE};
            }}

            /* Menu Dropdowns */
            QMenu {{
                background-color: {PRIMARY_BG};
                border: 2px solid {BORDER_COLOR};
                color: {PRIMARY_TEXT};
                padding: 8px;
                border-radius: 6px;
            }}
            QMenu::item {{
                background-color: {PRIMARY_BG};
                padding: 10px 20px;
                color: {PRIMARY_TEXT};
                border-radius: 4px;
                margin: 2px;
            }}
            QMenu::item:selected {{
                background-color: {PRIMARY_INTERACTIVE};
                color: {PRIMARY_TEXT};
            }}
            QMenu::item:hover {{
                background-color: {SECONDARY_INTERACTIVE};
                color: {PRIMARY_TEXT};
            }}
            QMenu::separator {{
                height: 2px;
                background-color: {BORDER_COLOR};
                margin: 8px 4px;
                border-radius: 1px;
            }}

            /* Splitter */
            QSplitter::handle {{
                background-color: {BORDER_COLOR};
            }}
            QSplitter::handle:horizontal {{
                width: 3px;
            }}
            QSplitter::handle:vertical {{
                height: 3px;
            }}

            /* Frame styling - reduce border visibility */
            QFrame {{
                background-color: {PRIMARY_BG};
                color: {PRIMARY_TEXT};
                border: none;
            }}

            /* Only show borders on specific frame types */
            QFrame[frameShape="1"] {{
                border: 1px solid {BORDER_COLOR};
            }}

            /* Checkable Selection Buttons - Using object name for specificity */
            QPushButton#selection_button {{
                background-color: #1A1A1A !important;
                color: {PRIMARY_TEXT} !important;
                border: 1px solid #444444 !important;
                font-weight: normal !important;
                border-radius: 6px !important;
                padding: 8px 16px !important;
            }}
            QPushButton#selection_button:checked {{
                background-color: {PRIMARY_INTERACTIVE} !important;
                color: {PRIMARY_TEXT} !important;
                border: 2px solid {PRIMARY_INTERACTIVE} !important;
                font-weight: bold !important;
            }}
            QPushButton#selection_button:hover:!checked {{
                background-color: #444444 !important;
                color: {PRIMARY_TEXT} !important;
                border: 1px solid #9575FF !important;
            }}
            QPushButton#selection_button:checked:hover {{
                background-color: #9575FF !important;
                color: {PRIMARY_TEXT} !important;
                border: 2px solid #9575FF !important;
            }}
            QPushButton#selection_button:pressed {{
                background-color: {PRIMARY_INTERACTIVE} !important;
            }}

            /* Enhanced Button Styling - General buttons */
            QPushButton {{
                background-color: {PRIMARY_INTERACTIVE};
                color: {PRIMARY_TEXT};
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 14px;
                font-family: 'Inter', sans-serif;
                min-height: 20px;
            }}
            QPushButton:hover {{
                background-color: #9575FF;
                color: {PRIMARY_TEXT};
                border: 2px solid #9575FF;
            }}
            QPushButton:pressed {{
                background-color: #5A3FBB;
                border: 2px solid {PRIMARY_TEXT};
            }}
            QPushButton:disabled {{
                background-color: #333333;
                color: #666666;
                border: 1px solid #444444;
            }}
            QPushButton:focus {{
                border: 2px solid {SECONDARY_INTERACTIVE};
                outline: none;
            }}

            /* Group Box styling - reduce borders */
            QGroupBox {{
                color: {PRIMARY_TEXT};
                background-color: {PRIMARY_BG};
                border: 1px solid {BORDER_COLOR};
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
                font-weight: bold;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: {PRIMARY_TEXT};
                background-color: {PRIMARY_BG};
                border: none;
            }}


        """)

        # Create tab widget
        self.tabs = QTabWidget()

        # Tab 1: Data Input
        input_tab = self.create_input_tab()
        self.tabs.addTab(input_tab, "Data Input")

        # Tab 2: Set Prediction
        set_prediction_tab = self.create_set_prediction_tab()
        self.tabs.addTab(set_prediction_tab, "Set Prediction")

        # Tab 3: Game Analysis
        analysis_tab = self.create_analysis_tab()
        self.tabs.addTab(analysis_tab, "Game Analysis")

        # Tab 4: Live Momentum
        patterns_tab = self.create_patterns_tab()
        self.tabs.addTab(patterns_tab, "Live Momentum")

        # Tab 5: Predictions
        predictions_tab = self.create_predictions_tab()
        self.tabs.addTab(predictions_tab, "Next Game Prediction")

        # Tab 6: Prediction Statistics
        statistics_tab = self.create_statistics_tab()
        self.tabs.addTab(statistics_tab, "Prediction Statistics")

        # Tab 7: Match Manager
        match_manager_tab = self.create_match_manager_tab()
        self.tabs.addTab(match_manager_tab, "Match Manager")

        # Connect tab change signal to refresh set prediction when switching to that tab
        self.tabs.currentChanged.connect(self.on_tab_changed)

        layout = QVBoxLayout(central_widget)
        layout.addWidget(self.tabs)

    def create_menu_bar(self):
        """Create the application menu bar"""
        menubar = self.menuBar()

        # Tools menu
        tools_menu = menubar.addMenu('Tools')

        # Player Data submenu
        player_data_menu = tools_menu.addMenu('Player Data')

        # Bulk Download action
        bulk_download_action = player_data_menu.addAction('Bulk Download ATP Players')
        bulk_download_action.triggered.connect(self.open_bulk_download)
        bulk_download_action.setToolTip('Download player profiles for all ATP players from Tennis Abstract')

        # Individual Download action
        individual_download_action = player_data_menu.addAction('Download Individual Player')
        individual_download_action.triggered.connect(self.open_individual_download)
        individual_download_action.setToolTip('Download a specific player profile')

        # Data Management submenu
        data_menu = tools_menu.addMenu('Data Management')

        # Export Statistics action (prediction statistics only)
        export_stats_action = data_menu.addAction('Export Prediction Statistics')
        export_stats_action.triggered.connect(self.export_statistics)

        # AI Learning submenu
        ai_menu = tools_menu.addMenu('AI Learning System')

        learning_dashboard_action = ai_menu.addAction('Learning Dashboard')
        learning_dashboard_action.triggered.connect(self.open_learning_dashboard)
        learning_dashboard_action.setToolTip('Monitor and configure adaptive learning system')

        optimize_weights_action = ai_menu.addAction('Optimize Weights Now')
        optimize_weights_action.triggered.connect(self.optimize_ai_weights)
        optimize_weights_action.setToolTip('Manually trigger conservative weight optimization (requires 20+ predictions, momentum factors need 50+)')

        # Enhanced Learning submenu
        ai_menu.addSeparator()

        enhanced_status_action = ai_menu.addAction('Enhanced Learning Status')
        enhanced_status_action.triggered.connect(self.show_enhanced_learning_status)
        enhanced_status_action.setToolTip('View enhanced historical/momentum balance learning status (conservative mode with 40+ prediction requirement)')

        enhanced_optimize_action = ai_menu.addAction('Optimize Enhanced Balances')
        enhanced_optimize_action.triggered.connect(self.force_enhanced_optimization)
        enhanced_optimize_action.setToolTip('Manually trigger enhanced balance optimization (conservative mode, requires 40+ predictions)')

        # Reset weights actions
        ai_menu.addSeparator()
        reset_weights_action = ai_menu.addAction('Reset All Learning Systems')
        reset_weights_action.triggered.connect(self.reset_all_weights)
        reset_weights_action.setToolTip('Reset ALL learning systems including Enhanced Learning V2 (clears all learning progress)')

        # Enhanced Learning V2 specific reset
        if ENHANCED_LEARNING_AVAILABLE:
            reset_enhanced_action = ai_menu.addAction('Reset Enhanced Learning V2 Only')
            reset_enhanced_action.triggered.connect(self.reset_enhanced_learning_v2)
            reset_enhanced_action.setToolTip('Reset only the Enhanced Learning System V2 (tournament-specific learning)')

        # Help menu
        help_menu = menubar.addMenu('Help')

        about_action = help_menu.addAction('About')
        about_action.triggered.connect(self.show_about)

    def open_bulk_download(self):
        """Open the bulk download GUI"""
        try:
            from bulk_download_gui import BulkDownloadGUI

            # Create and show bulk download window
            self.bulk_download_window = BulkDownloadGUI()
            self.bulk_download_window.show()

        except ImportError:
            QMessageBox.warning(
                self, "Module Not Found",
                "Bulk download module not available. Please ensure bulk_download_gui.py is in the same directory."
            )
        except Exception as e:
            QMessageBox.critical(
                self, "Error",
                f"Failed to open bulk download window: {str(e)}"
            )

    def open_individual_download(self):
        """Open individual player download dialog"""
        try:
            show_improved_download_dialog(self)
        except Exception as e:
            QMessageBox.critical(
                self, "Error",
                f"Failed to open download dialog: {str(e)}"
            )



    def show_about(self):
        """Show about dialog"""
        about_text = """
        <h3>Enhanced Tennis Probability Calculator</h3>
        <p>Advanced tennis match analysis and prediction system</p>
        <p><b>Features:</b></p>
        <ul>
        <li>Real-time game analysis</li>
        <li>AI-powered set predictions</li>
        <li>Player profile downloads</li>
        <li>Bulk ATP player data management</li>
        <li>Match session management</li>
        <li>Prediction tracking and statistics</li>
        </ul>
        <p><b>Version:</b> 4.0</p>
        """
        QMessageBox.about(self, "About", about_text)

    def create_input_tab(self):
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Player information
        player_group = QGroupBox("Player Information")
        player_layout = QVBoxLayout(player_group)

        # Single row: Player names, codes, and favorite selection
        names_layout = QHBoxLayout()

        # Player 1
        self.player1_name = QLineEdit()
        self.player1_name.setPlaceholderText("Player 1 Name")
        self.player1_code = QLineEdit()
        self.player1_code.setPlaceholderText("Code")
        self.player1_code.setMaximumWidth(80)  # Increased size for 4-5 letter codes
        self.player1_code.setMinimumWidth(80)

        names_layout.addWidget(QLabel("Player 1:"))
        names_layout.addWidget(self.player1_name)
        names_layout.addWidget(self.player1_code)

        # Player 2
        self.player2_name = QLineEdit()
        self.player2_name.setPlaceholderText("Player 2 Name")
        self.player2_code = QLineEdit()
        self.player2_code.setPlaceholderText("Code")
        self.player2_code.setMaximumWidth(80)  # Increased size for 4-5 letter codes
        self.player2_code.setMinimumWidth(80)

        names_layout.addWidget(QLabel("Player 2:"))
        names_layout.addWidget(self.player2_name)
        names_layout.addWidget(self.player2_code)

        # Pre-match Favorite on same line
        names_layout.addWidget(QLabel("Pre-match Favorite:"))

        self.player1_favorite_btn = QPushButton("Player 1")
        self.player1_favorite_btn.setCheckable(True)
        self.player1_favorite_btn.setObjectName("selection_button")
        self.player1_favorite_btn.clicked.connect(lambda: self.set_favorite(1))
        names_layout.addWidget(self.player1_favorite_btn)

        self.player2_favorite_btn = QPushButton("Player 2")
        self.player2_favorite_btn.setCheckable(True)
        self.player2_favorite_btn.setObjectName("selection_button")
        self.player2_favorite_btn.clicked.connect(lambda: self.set_favorite(2))
        names_layout.addWidget(self.player2_favorite_btn)

        self.no_favorite_btn = QPushButton("No Favorite")
        self.no_favorite_btn.setCheckable(True)
        self.no_favorite_btn.setObjectName("selection_button")
        self.no_favorite_btn.setChecked(True)  # Default selection
        self.no_favorite_btn.clicked.connect(lambda: self.set_favorite(0))
        names_layout.addWidget(self.no_favorite_btn)

        # Odds input
        self.favorite_odds = QLineEdit()
        self.favorite_odds.setPlaceholderText("e.g., 1.45")
        self.favorite_odds.setMaximumWidth(80)
        self.favorite_odds.setEnabled(False)  # Disabled by default
        names_layout.addWidget(QLabel("Odds:"))
        names_layout.addWidget(self.favorite_odds)

        names_layout.addStretch()
        player_layout.addLayout(names_layout)

        layout.addWidget(player_group)

        # Connect name changes to auto-generate codes
        self.player1_name.textChanged.connect(lambda: self.auto_generate_code(self.player1_name, self.player1_code))
        self.player2_name.textChanged.connect(lambda: self.auto_generate_code(self.player2_name, self.player2_code))

        # Match information group
        match_info_group = QGroupBox("Match Information")
        match_info_layout = QVBoxLayout(match_info_group)

        # First row: Match format, Tournament Level, Surface, Current Set, Starting Server
        format_set_layout = QHBoxLayout()

        # Match format
        self.match_format = QComboBox()
        self.match_format.addItems(["Best of 3", "Best of 5"])
        format_set_layout.addWidget(QLabel("Match Format:"))
        format_set_layout.addWidget(self.match_format)

        # Tournament Level (after Match Format)
        self.tournament_level = QComboBox()
        self.tournament_level.addItems(["ATP", "Challenger", "WTA", "Mixed"])
        self.tournament_level.setCurrentText("ATP")  # Default to ATP
        tournament_level_label = QLabel("Tournament Level:")
        tournament_level_label.setStyleSheet(f"font-weight: bold; color: {PRIMARY_TEXT};")
        format_set_layout.addWidget(tournament_level_label)
        format_set_layout.addWidget(self.tournament_level)

        # Surface type (right after Tournament Level)
        self.surface_type = QComboBox()
        self.surface_type.addItems(["Hard", "Clay", "Grass", "Indoor Hard"])
        format_set_layout.addWidget(QLabel("Surface:"))
        format_set_layout.addWidget(self.surface_type)

        # Set number
        self.set_number = QComboBox()
        self.set_number.addItems(["Set 1", "Set 2", "Set 3", "Set 4", "Set 5"])
        current_set_label = QLabel("Current Set:")
        current_set_label.setStyleSheet(f"font-weight: bold; color: {PRIMARY_TEXT};")
        format_set_layout.addWidget(current_set_label)
        format_set_layout.addWidget(self.set_number)

        # Starting Server (right after Current Set)
        format_set_layout.addWidget(QLabel("Starting Server:"))

        self.player1_server_btn = QPushButton("Player 1")
        self.player1_server_btn.setCheckable(True)
        self.player1_server_btn.setObjectName("selection_button")
        self.player1_server_btn.setChecked(True)  # Default to Player 1
        self.player1_server_btn.clicked.connect(lambda: self.set_starting_server(1, manual_override=True))
        format_set_layout.addWidget(self.player1_server_btn)

        self.player2_server_btn = QPushButton("Player 2")
        self.player2_server_btn.setCheckable(True)
        self.player2_server_btn.setObjectName("selection_button")
        self.player2_server_btn.clicked.connect(lambda: self.set_starting_server(2, manual_override=True))
        format_set_layout.addWidget(self.player2_server_btn)

        # Add auto-detection indicator
        self.auto_detect_label = QLabel("🤖 Auto-detect enabled")
        self.auto_detect_label.setStyleSheet("color: #7054DD; font-size: 10px;")
        format_set_layout.addWidget(self.auto_detect_label)

        format_set_layout.addStretch()
        match_info_layout.addLayout(format_set_layout)

        # Second row: Tournament Name and Preserve Tournament Checkbox
        tournament_layout = QHBoxLayout()

        # Tournament Name (optional, for better classification)
        self.tournament_name = QLineEdit()
        self.tournament_name.setPlaceholderText("e.g., Wimbledon, Roland Garros, Miami Open...")
        tournament_layout.addWidget(QLabel("Tournament Name:"))
        tournament_layout.addWidget(self.tournament_name)

        # Preserve Tournament Checkbox for faster data entry
        self.preserve_tournament_checkbox = QCheckBox("🔒 Keep Tournament Info")
        self.preserve_tournament_checkbox.setToolTip("When checked, Tournament Level and Tournament Name will not be cleared when using 'Clear All'")
        self.preserve_tournament_checkbox.setStyleSheet(f"color: {PRIMARY_TEXT}; font-weight: bold;")
        tournament_layout.addWidget(self.preserve_tournament_checkbox)

        tournament_layout.addStretch()
        match_info_layout.addLayout(tournament_layout)

        # Second row: Previous sets (if applicable)
        prev_sets_layout = QHBoxLayout()
        prev_sets_layout.addWidget(QLabel("Previous Sets:"))
        self.prev_set_winners = []
        self.auto_filled_sets = set()  # Track which sets were auto-filled
        for i in range(4):  # Max 4 previous sets
            label = QLabel(f"Set {i+1}:")
            combo = QComboBox()
            combo.addItem("-")
            combo.setMaximumWidth(180)  # Increased to 180px to accommodate full names
            combo.setMinimumWidth(180)  # Also set minimum width for consistency
            # Connect to auto-fill logic with index tracking
            combo.currentIndexChanged.connect(lambda state, idx=i: self.on_set_selection_changed(idx))
            label.setVisible(False)
            combo.setVisible(False)
            prev_sets_layout.addWidget(label)
            prev_sets_layout.addWidget(combo)
            self.prev_set_winners.append((label, combo))

        prev_sets_layout.addStretch()
        match_info_layout.addLayout(prev_sets_layout)


        layout.addWidget(match_info_group)

        # Update previous sets visibility when set number changes
        self.set_number.currentIndexChanged.connect(self.update_previous_sets_visibility)
        self.match_format.currentIndexChanged.connect(self.update_set_number_options)



        # Match data
        data_group = QGroupBox("Match Data")
        data_layout = QVBoxLayout(data_group)

        data_layout.addWidget(QLabel("Paste match statistics (point-by-point data):"))
        self.match_data = QPlainTextEdit()
        self.match_data.setMinimumHeight(250)  # Reduced from 300 to 250
        self.match_data.setMaximumHeight(400)  # Add maximum height to prevent excessive growth
        # Connect to auto-extract player codes and detect starting server when data is pasted
        self.match_data.textChanged.connect(self.auto_extract_codes_from_data)
        self.match_data.textChanged.connect(self.auto_detect_starting_server)
        data_layout.addWidget(self.match_data)

        layout.addWidget(data_group)

        # Buttons
        button_layout = QHBoxLayout()

        self.analyze_button = QPushButton("Analyze Match")
        self.analyze_button.clicked.connect(self.analyze_match)
        button_layout.addWidget(self.analyze_button)

        # Only keep the new draft save button (removed old "Save to Queue")
        self.save_draft_button = QPushButton("Save to Queue")
        self.save_draft_button.clicked.connect(self.save_draft_to_queue)
        self.save_draft_button.setStyleSheet(f"QPushButton {{ background-color: {PRIMARY_INTERACTIVE}; color: {PRIMARY_TEXT}; }}")
        button_layout.addWidget(self.save_draft_button)

        self.clear_button = QPushButton("Clear All")
        self.clear_button.clicked.connect(self.clear_all)
        button_layout.addWidget(self.clear_button)

        # Add stretch to push right-side buttons to the right
        button_layout.addStretch()

        # Enhanced Learning System buttons (moved to right side)
        if ENHANCED_LEARNING_AVAILABLE:
            self.learning_dashboard_button = QPushButton("Learning Dashboard")
            self.learning_dashboard_button.clicked.connect(self.open_learning_dashboard)
            self.learning_dashboard_button.setStyleSheet(f"QPushButton {{ background-color: {PRIMARY_INTERACTIVE}; color: {PRIMARY_TEXT}; }}")
            button_layout.addWidget(self.learning_dashboard_button)

            self.run_validation_button = QPushButton("Run Validation")
            self.run_validation_button.clicked.connect(self.run_system_validation)
            self.run_validation_button.setStyleSheet(f"QPushButton {{ background-color: {WARNING_COLOR}; color: {PRIMARY_TEXT}; }}")
            button_layout.addWidget(self.run_validation_button)

        # Monitor integration buttons (on right side)
        self.check_monitor_button = QPushButton("Check for Monitor Data")
        self.check_monitor_button.clicked.connect(self.check_for_monitor_data)
        self.check_monitor_button.setStyleSheet(f"QPushButton {{ background-color: {PRIMARY_INTERACTIVE}; color: {PRIMARY_TEXT}; }}")
        button_layout.addWidget(self.check_monitor_button)

        # Add toggle for automatic monitor checking
        self.monitor_auto_check = QCheckBox("Auto-check monitor data")
        self.monitor_auto_check.setChecked(self.monitor_enabled)
        self.monitor_auto_check.stateChanged.connect(self.toggle_monitor_checking)
        button_layout.addWidget(self.monitor_auto_check)

        layout.addLayout(button_layout)

        # Update serving buttons when player codes OR names change
        self.player1_code.textChanged.connect(self.update_serving_combo)
        self.player2_code.textChanged.connect(self.update_serving_combo)
        self.player1_name.textChanged.connect(self.update_serving_combo)
        self.player2_name.textChanged.connect(self.update_serving_combo)

        return widget

    def create_analysis_tab(self):
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Game-by-game table
        layout.addWidget(QLabel("Game-by-Game Analysis:"))

        self.games_table = QTableWidget()
        self.games_table.setColumnCount(9)
        self.games_table.setHorizontalHeaderLabels([
            "Game", "Server", "Winner", "Result", "First Point",
            "3-Point Run", "Break Points", "Outcome", "Momentum"
        ])

        header = self.games_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)

        # Apply dark mode styling to the games table
        self.apply_table_styling(self.games_table)

        layout.addWidget(self.games_table)

        # Export button
        export_button = QPushButton("Export Analysis")
        export_button.clicked.connect(self.export_analysis)
        layout.addWidget(export_button)

        return widget

    def create_patterns_tab(self):
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)  # Add spacing between player sections

        # Create a scroll area for better handling of content
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        scroll_layout.setSpacing(15)

        # Serving patterns for each player
        self.player1_patterns = self.create_pattern_widget("Player 1")
        self.player2_patterns = self.create_pattern_widget("Player 2")

        scroll_layout.addWidget(self.player1_patterns)
        scroll_layout.addWidget(self.player2_patterns)
        scroll_layout.addStretch()  # Add stretch to push content to top

        scroll_area.setWidget(scroll_widget)
        layout.addWidget(scroll_area)

        return widget

    def create_pattern_widget(self, player_title):
        # Main player group with enhanced styling
        group = QGroupBox(f"📊 {player_title.upper()} LIVE MOMENTUM")
        group.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                font-size: 13px;
                color: {PRIMARY_TEXT};
                background-color: {PRIMARY_BG};
                border: 2px solid {BORDER_COLOR};
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: {PRIMARY_TEXT};
                background-color: {PRIMARY_BG};
            }}
        """)
        main_layout = QVBoxLayout(group)

        # Create organized sections
        labels = {}

        # 1. CORE PERFORMANCE SECTION
        core_group = QGroupBox("🎯 Core Performance")
        core_group.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                font-size: 11px;
                color: {PRIMARY_TEXT};
                background-color: {PRIMARY_BG};
                border: 1px solid {SUCCESS_COLOR};
                border-radius: 5px;
                margin-top: 8px;
                padding-top: 5px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 8px;
                padding: 0 3px 0 3px;
                color: {PRIMARY_TEXT};
                background-color: {PRIMARY_BG};
            }}
        """)
        core_layout = QVBoxLayout(core_group)

        # Games Held with progress bar
        hold_container = QHBoxLayout()
        labels['hold_percentage'] = QLabel("Games Held: N/A")
        labels['hold_percentage'].setFont(QFont("Inter", 10, QFont.Bold))
        labels['hold_percentage_bar'] = QProgressBar()
        labels['hold_percentage_bar'].setMaximum(100)
        labels['hold_percentage_bar'].setMaximumHeight(15)
        labels['hold_percentage_bar'].setStyleSheet(f"""
            QProgressBar {{
                border: 1px solid {BORDER_COLOR};
                border-radius: 3px;
                text-align: center;
                background-color: {PRIMARY_BG};
                color: {PRIMARY_TEXT};
            }}
            QProgressBar::chunk {{
                background-color: {SUCCESS_COLOR};
                border-radius: 2px;
            }}
        """)
        hold_container.addWidget(labels['hold_percentage'], 1)
        hold_container.addWidget(labels['hold_percentage_bar'], 1)
        core_layout.addLayout(hold_container)

        # Hold Streak
        labels['hold_streak'] = QLabel("Current Hold Streak: N/A")
        labels['hold_streak'].setFont(QFont("Inter", 10))
        core_layout.addWidget(labels['hold_streak'])

        # Service Consistency with progress bar
        consistency_container = QHBoxLayout()
        labels['consistency_score'] = QLabel("Service Consistency: N/A")
        labels['consistency_score'].setFont(QFont("Inter", 10))
        labels['consistency_bar'] = QProgressBar()
        labels['consistency_bar'].setMaximum(100)
        labels['consistency_bar'].setMaximumHeight(15)
        labels['consistency_bar'].setStyleSheet(f"""
            QProgressBar {{
                border: 1px solid {BORDER_COLOR};
                border-radius: 3px;
                text-align: center;
                background-color: {PRIMARY_BG};
                color: {PRIMARY_TEXT};
            }}
            QProgressBar::chunk {{
                background-color: {PRIMARY_INTERACTIVE};
                border-radius: 2px;
            }}
        """)
        consistency_container.addWidget(labels['consistency_score'], 1)
        consistency_container.addWidget(labels['consistency_bar'], 1)
        core_layout.addLayout(consistency_container)

        main_layout.addWidget(core_group)

        # 2. MOMENTUM & FORM SECTION
        momentum_group = QGroupBox("⚡ Momentum & Form")
        momentum_group.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                font-size: 11px;
                color: {PRIMARY_TEXT};
                background-color: {PRIMARY_BG};
                border: 1px solid {WARNING_COLOR};
                border-radius: 5px;
                margin-top: 8px;
                padding-top: 5px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 8px;
                padding: 0 3px 0 3px;
                color: {PRIMARY_TEXT};
                background-color: {PRIMARY_BG};
            }}
        """)
        momentum_layout = QVBoxLayout(momentum_group)

        # Current Momentum (prominent)
        labels['momentum'] = QLabel("Current Momentum: N/A")
        labels['momentum'].setFont(QFont("Inter", 11, QFont.Bold))
        momentum_layout.addWidget(labels['momentum'])

        # Momentum details in a row
        momentum_details = QHBoxLayout()
        labels['momentum_intensity'] = QLabel("Intensity: N/A")
        labels['momentum_intensity'].setFont(QFont("Inter", 9))
        labels['momentum_duration'] = QLabel("Duration: N/A")
        labels['momentum_duration'].setFont(QFont("Inter", 9))
        momentum_details.addWidget(labels['momentum_intensity'])
        momentum_details.addWidget(labels['momentum_duration'])
        momentum_layout.addLayout(momentum_details)

        # Recent form indicators
        form_row = QHBoxLayout()
        labels['three_point_runs'] = QLabel("3-Point Runs: N/A")
        labels['three_point_runs'].setFont(QFont("Inter", 9))
        labels['consecutive_015'] = QLabel("0-15 Starts: N/A")
        labels['consecutive_015'].setFont(QFont("Inter", 9))
        form_row.addWidget(labels['three_point_runs'])
        form_row.addWidget(labels['consecutive_015'])
        momentum_layout.addLayout(form_row)

        main_layout.addWidget(momentum_group)

        # 3. PRESSURE & MENTAL SECTION
        pressure_group = QGroupBox("🧠 Pressure & Mental")
        pressure_group.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                font-size: 11px;
                color: {PRIMARY_TEXT};
                background-color: {PRIMARY_BG};
                border: 1px solid {ERROR_COLOR};
                border-radius: 5px;
                margin-top: 8px;
                padding-top: 5px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 8px;
                padding: 0 3px 0 3px;
                color: {PRIMARY_TEXT};
                background-color: {PRIMARY_BG};
            }}
        """)
        pressure_layout = QVBoxLayout(pressure_group)

        # Pressure metrics in a row
        pressure_row = QHBoxLayout()
        labels['pressure_index'] = QLabel("Pressure Index: N/A")
        labels['pressure_index'].setFont(QFont("Inter", 10))
        labels['mental_fatigue'] = QLabel("Mental Fatigue: N/A")
        labels['mental_fatigue'].setFont(QFont("Inter", 10))
        pressure_row.addWidget(labels['pressure_index'])
        pressure_row.addWidget(labels['mental_fatigue'])
        pressure_layout.addLayout(pressure_row)

        # Clutch performance and break points
        clutch_row = QHBoxLayout()
        labels['clutch_rate'] = QLabel("Clutch Performance: N/A")
        labels['clutch_rate'].setFont(QFont("Inter", 10))
        labels['break_points'] = QLabel("Break Points: N/A")
        labels['break_points'].setFont(QFont("Inter", 10))
        clutch_row.addWidget(labels['clutch_rate'])
        clutch_row.addWidget(labels['break_points'])
        pressure_layout.addLayout(clutch_row)

        main_layout.addWidget(pressure_group)

        # 4. ADVANCED RHYTHM SECTION (Collapsible)
        rhythm_group = QGroupBox("📈 Advanced Serving Rhythm")
        rhythm_group.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                font-size: 11px;
                color: {PRIMARY_TEXT};
                background-color: {PRIMARY_BG};
                border: 1px solid {SECONDARY_INTERACTIVE};
                border-radius: 5px;
                margin-top: 8px;
                padding-top: 5px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 8px;
                padding: 0 3px 0 3px;
                color: {PRIMARY_TEXT};
                background-color: {PRIMARY_BG};
            }}
        """)
        rhythm_layout = QVBoxLayout(rhythm_group)

        # Recovery patterns
        recovery_label = QLabel("Recovery Patterns:")
        recovery_label.setFont(QFont("Inter", 9, QFont.Bold))
        rhythm_layout.addWidget(recovery_label)

        recovery_row1 = QHBoxLayout()
        labels['recovery_0_15'] = QLabel("0-15: N/A")
        labels['recovery_0_15'].setFont(QFont("Inter", 9))
        labels['recovery_0_30'] = QLabel("0-30: N/A")
        labels['recovery_0_30'].setFont(QFont("Inter", 9))
        labels['recovery_0_40'] = QLabel("0-40: N/A")
        labels['recovery_0_40'].setFont(QFont("Inter", 9))
        recovery_row1.addWidget(labels['recovery_0_15'])
        recovery_row1.addWidget(labels['recovery_0_30'])
        recovery_row1.addWidget(labels['recovery_0_40'])
        rhythm_layout.addLayout(recovery_row1)

        # Closing patterns
        closing_label = QLabel("Closing Patterns:")
        closing_label.setFont(QFont("Inter", 9, QFont.Bold))
        rhythm_layout.addWidget(closing_label)

        closing_row1 = QHBoxLayout()
        labels['closing_40_0'] = QLabel("40-0: N/A")
        labels['closing_40_0'].setFont(QFont("Inter", 9))
        labels['closing_40_15'] = QLabel("40-15: N/A")
        labels['closing_40_15'].setFont(QFont("Inter", 9))
        labels['closing_40_30'] = QLabel("40-30: N/A")
        labels['closing_40_30'].setFont(QFont("Inter", 9))
        closing_row1.addWidget(labels['closing_40_0'])
        closing_row1.addWidget(labels['closing_40_15'])
        closing_row1.addWidget(labels['closing_40_30'])
        rhythm_layout.addLayout(closing_row1)

        # Additional rhythm metrics
        rhythm_extras = QHBoxLayout()
        labels['deuce_win_rate'] = QLabel("Deuce Win Rate: N/A")
        labels['deuce_win_rate'].setFont(QFont("Inter", 9))
        labels['service_tempo'] = QLabel("Service Tempo: N/A")
        labels['service_tempo'].setFont(QFont("Inter", 9))
        rhythm_extras.addWidget(labels['deuce_win_rate'])
        rhythm_extras.addWidget(labels['service_tempo'])
        rhythm_layout.addLayout(rhythm_extras)

        main_layout.addWidget(rhythm_group)

        # Store labels for updates
        if player_title == "Player 1":
            self.player1_pattern_labels = labels
        else:
            self.player2_pattern_labels = labels

        return group

    def create_predictions_tab(self):
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Next game prediction
        prediction_group = QGroupBox("Next Game Prediction")
        prediction_layout = QVBoxLayout(prediction_group)

        self.next_server_label = QLabel("Next Server: N/A")
        self.next_server_label.setFont(QFont("Inter", 12, QFont.Bold))
        prediction_layout.addWidget(self.next_server_label)

        self.predicted_winner_label = QLabel("Predicted Winner: N/A")
        self.predicted_winner_label.setFont(QFont("Inter", 12, QFont.Bold))
        prediction_layout.addWidget(self.predicted_winner_label)

        # Probability bars
        prob_layout = QHBoxLayout()
        prob_layout.addWidget(QLabel("Hold Probability:"))
        self.hold_prob_bar = QProgressBar()
        self.hold_prob_bar.setMaximum(100)
        prob_layout.addWidget(self.hold_prob_bar)
        prediction_layout.addLayout(prob_layout)

        prob_layout2 = QHBoxLayout()
        prob_layout2.addWidget(QLabel("Break Probability:"))
        self.break_prob_bar = QProgressBar()
        self.break_prob_bar.setMaximum(100)
        prob_layout2.addWidget(self.break_prob_bar)
        prediction_layout.addLayout(prob_layout2)

        self.confidence_label = QLabel("Confidence: N/A")
        prediction_layout.addWidget(self.confidence_label)

        layout.addWidget(prediction_group)

        # Momentum factors
        momentum_group = QGroupBox("Momentum Factors")
        momentum_layout = QVBoxLayout(momentum_group)

        self.momentum_text = QTextEdit()
        self.momentum_text.setReadOnly(True)
        self.momentum_text.setMaximumHeight(200)
        momentum_layout.addWidget(self.momentum_text)

        layout.addWidget(momentum_group)

        # Insights
        insights_group = QGroupBox("Key Insights")
        insights_layout = QVBoxLayout(insights_group)

        self.insights_text = QTextEdit()
        self.insights_text.setReadOnly(True)
        insights_layout.addWidget(self.insights_text)

        layout.addWidget(insights_group)

        return widget

    def create_set_prediction_tab(self):
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Enhanced match info section (consolidated requirements, score, momentum)
        info_group = QGroupBox("Match Information")
        info_layout = QVBoxLayout(info_group)
        info_group.setMaximumHeight(160)  # Increased from 120 to 160

        # Create a grid layout for improved display
        info_grid = QGridLayout()
        info_grid.setVerticalSpacing(8)  # Add vertical spacing between rows
        info_grid.setHorizontalSpacing(15)  # Add horizontal spacing between columns

        # Requirements/Status
        self.set_requirements_label = QLabel("At least 6 games must be played for set prediction")
        self.set_requirements_label.setFont(QFont("Inter", 10))  # Increased from 9 to 10
        # Create styled labels for better readability
        status_label = QLabel("Status:")
        status_label.setFont(QFont("Inter", 10, QFont.Bold))
        status_label.setStyleSheet(f"color: {PRIMARY_TEXT};")
        info_grid.addWidget(status_label, 0, 0)
        info_grid.addWidget(self.set_requirements_label, 0, 1)

        # Current Set
        self.current_set_label = QLabel("Set: N/A")
        self.current_set_label.setFont(QFont("Inter", 10, QFont.Bold))  # Increased from 9 to 10
        self.current_set_label.setStyleSheet(f"color: {SECONDARY_INTERACTIVE}; font-weight: bold;")
        current_set_label = QLabel("Current Set:")
        current_set_label.setFont(QFont("Inter", 10, QFont.Bold))
        current_set_label.setStyleSheet(f"color: {PRIMARY_TEXT};")
        info_grid.addWidget(current_set_label, 1, 0)
        info_grid.addWidget(self.current_set_label, 1, 1)

        # Current score
        self.current_set_score_label = QLabel("Score: N/A")
        self.current_set_score_label.setFont(QFont("Inter", 10, QFont.Bold))  # Increased from 9 to 10
        score_label = QLabel("Score:")
        score_label.setFont(QFont("Inter", 10, QFont.Bold))
        score_label.setStyleSheet(f"color: {PRIMARY_TEXT};")
        info_grid.addWidget(score_label, 2, 0)
        info_grid.addWidget(self.current_set_score_label, 2, 1)

        # Games played
        self.games_played_label = QLabel("Games played: N/A")
        self.games_played_label.setFont(QFont("Inter", 10))  # Increased from 9 to 10
        games_label = QLabel("Games:")
        games_label.setFont(QFont("Inter", 10, QFont.Bold))
        games_label.setStyleSheet(f"color: {PRIMARY_TEXT};")
        info_grid.addWidget(games_label, 3, 0)
        info_grid.addWidget(self.games_played_label, 3, 1)

        # Hold rates (momentum info)
        self.player1_hold_prob_label = QLabel("Player 1 Hold Rate: N/A")
        self.player1_hold_prob_label.setFont(QFont("Inter", 10))  # Increased from 9 to 10
        self.player2_hold_prob_label = QLabel("Player 2 Hold Rate: N/A")
        self.player2_hold_prob_label.setFont(QFont("Inter", 10))  # Increased from 9 to 10
        hold_rates_label = QLabel("Hold Rates:")
        hold_rates_label.setFont(QFont("Inter", 10, QFont.Bold))
        hold_rates_label.setStyleSheet(f"color: {PRIMARY_TEXT};")
        info_grid.addWidget(hold_rates_label, 4, 0)
        hold_rates_layout = QVBoxLayout()
        hold_rates_layout.addWidget(self.player1_hold_prob_label)
        hold_rates_layout.addWidget(self.player2_hold_prob_label)
        hold_rates_widget = QWidget()
        hold_rates_widget.setLayout(hold_rates_layout)
        info_grid.addWidget(hold_rates_widget, 4, 1)

        info_layout.addLayout(info_grid)
        layout.addWidget(info_group)

        # AI ANALYSIS SECTION (PRIMARY - most prominent)
        ai_group = QGroupBox("🤖 AI ANALYSIS (Primary Prediction)")
        ai_group.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                font-size: 16px;
                color: {PRIMARY_TEXT};
                background-color: {PRIMARY_BG};
                border: 3px solid {PRIMARY_INTERACTIVE};
                border-radius: 10px;
                margin-top: 12px;
                padding-top: 12px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 12px;
                padding: 0 8px 0 8px;
                color: {PRIMARY_TEXT};
                background-color: {PRIMARY_BG};
            }}
        """)
        ai_layout = QVBoxLayout(ai_group)

        # Compact API configuration
        api_config_layout = QHBoxLayout()
        self.api_key_input = QLineEdit()
        self.api_key_input.setEchoMode(QLineEdit.Password)
        self.api_key_input.setPlaceholderText("API Key...")
        self.api_key_input.setMaximumWidth(200)
        api_config_layout.addWidget(self.api_key_input)

        self.set_api_key_button = QPushButton("Set Key")
        self.set_api_key_button.clicked.connect(self.set_gemini_api_key)
        self.set_api_key_button.setMaximumWidth(80)
        api_config_layout.addWidget(self.set_api_key_button)

        self.persist_api_key_checkbox = QCheckBox("Remember")
        self.persist_api_key_checkbox.setChecked(config.get_persist_api_keys())
        self.persist_api_key_checkbox.toggled.connect(self.toggle_api_key_persistence)
        api_config_layout.addWidget(self.persist_api_key_checkbox)

        settings_button = QPushButton("Settings")
        settings_button.clicked.connect(self.show_settings_dialog)
        settings_button.setMaximumWidth(80)
        api_config_layout.addWidget(settings_button)

        api_config_layout.addStretch()
        ai_layout.addLayout(api_config_layout)

        # Prominent AI Analysis button and results
        ai_main_layout = QHBoxLayout()

        # Left side: Button and status
        ai_control_layout = QVBoxLayout()
        self.ai_analyze_button = QPushButton("🚀 GET AI ANALYSIS")
        self.ai_analyze_button.clicked.connect(self.get_ai_analysis)
        self.ai_analyze_button.setEnabled(False)
        self.ai_analyze_button.setMinimumHeight(50)
        self.ai_analyze_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {PRIMARY_INTERACTIVE};
                color: {PRIMARY_TEXT};
                border: none;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
            }}
            QPushButton:hover {{
                background-color: #9575FF;
            }}
            QPushButton:disabled {{
                background-color: #333333;
                color: #666666;
            }}
        """)
        ai_control_layout.addWidget(self.ai_analyze_button)

        self.ai_status_label = QLabel("API key required")
        self.ai_status_label.setStyleSheet(f"color: {WARNING_COLOR}; font-size: 11px;")
        self.ai_status_label.setAlignment(Qt.AlignCenter)
        ai_control_layout.addWidget(self.ai_status_label)

        # Right side: AI Probabilities (prominent display)
        ai_results_layout = QVBoxLayout()
        ai_prob_layout = QHBoxLayout()

        self.ai_player1_prob_label = QLabel("Player 1: N/A")
        self.ai_player1_prob_label.setFont(QFont("Inter", 16, QFont.Bold))
        self.ai_player1_prob_label.setAlignment(Qt.AlignCenter)
        self.ai_player1_prob_label.setStyleSheet(f"color: {SUCCESS_COLOR}; border: 2px solid {SUCCESS_COLOR}; padding: 8px; border-radius: 6px; background-color: {PRIMARY_BG};")
        ai_prob_layout.addWidget(self.ai_player1_prob_label)

        ai_vs_label = QLabel("VS")
        ai_vs_label.setFont(QFont("Inter", 18, QFont.Bold))
        ai_vs_label.setAlignment(Qt.AlignCenter)
        ai_vs_label.setStyleSheet(f"color: {PRIMARY_TEXT}; margin: 0 15px;")
        ai_prob_layout.addWidget(ai_vs_label)

        self.ai_player2_prob_label = QLabel("Player 2: N/A")
        self.ai_player2_prob_label.setFont(QFont("Inter", 16, QFont.Bold))
        self.ai_player2_prob_label.setAlignment(Qt.AlignCenter)
        self.ai_player2_prob_label.setStyleSheet(f"color: {PRIMARY_INTERACTIVE}; border: 2px solid {PRIMARY_INTERACTIVE}; padding: 8px; border-radius: 6px; background-color: {PRIMARY_BG};")
        ai_prob_layout.addWidget(self.ai_player2_prob_label)

        ai_results_layout.addLayout(ai_prob_layout)

        # Record outcome buttons positioned close to AI probabilities
        outcome_buttons_layout = QHBoxLayout()
        outcome_buttons_layout.addStretch()

        self.player1_win_button = QPushButton("Player 1 Won")
        self.player1_win_button.clicked.connect(lambda: self.record_outcome(1))
        self.player1_win_button.setEnabled(False)
        self.player1_win_button.setMinimumHeight(35)
        self.player1_win_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {SUCCESS_COLOR};
                color: {PRIMARY_TEXT};
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 13px;
            }}
            QPushButton:hover {{
                background-color: #66BB6A;
                border: 2px solid #81C784;
            }}
            QPushButton:disabled {{
                background-color: #333333;
                color: #666666;
            }}
        """)
        outcome_buttons_layout.addWidget(self.player1_win_button)

        self.player2_win_button = QPushButton("Player 2 Won")
        self.player2_win_button.clicked.connect(lambda: self.record_outcome(2))
        self.player2_win_button.setEnabled(False)
        self.player2_win_button.setMinimumHeight(35)
        self.player2_win_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {PRIMARY_INTERACTIVE};
                color: {PRIMARY_TEXT};
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 13px;
            }}
            QPushButton:hover {{
                background-color: #66BB6A;
                border: 2px solid #81C784;
            }}
            QPushButton:disabled {{
                background-color: #333333;
                color: #666666;
            }}
        """)
        outcome_buttons_layout.addWidget(self.player2_win_button)

        outcome_buttons_layout.addStretch()
        ai_results_layout.addLayout(outcome_buttons_layout)

        # Add layouts to main horizontal layout
        ai_main_layout.addLayout(ai_control_layout, 1)
        ai_main_layout.addLayout(ai_results_layout, 2)
        ai_layout.addLayout(ai_main_layout)

        # Compact AI analysis text
        self.ai_analysis_text = QTextEdit()
        self.ai_analysis_text.setReadOnly(True)
        self.ai_analysis_text.setMaximumHeight(100)
        self.ai_analysis_text.setFont(QFont("Inter", 10))
        self.ai_analysis_text.setPlaceholderText("AI analysis details will appear here...")
        ai_layout.addWidget(self.ai_analysis_text)

        # Prediction info label
        self.current_pred_info_label = QLabel("No prediction to record")
        self.current_pred_info_label.setFont(QFont("Inter", 10))
        self.current_pred_info_label.setAlignment(Qt.AlignCenter)
        self.current_pred_info_label.setStyleSheet(f"color: {PRIMARY_TEXT}; margin-top: 5px;")
        ai_layout.addWidget(self.current_pred_info_label)

        layout.addWidget(ai_group)

        # MATH SET PREDICTION SECTION (de-emphasized, smaller)
        prediction_group = QGroupBox("📊 Math Prediction")
        prediction_group.setStyleSheet(f"""
            QGroupBox {{
                font-weight: normal;
                font-size: 12px;
                color: {PRIMARY_TEXT};
                background-color: {PRIMARY_BG};
                border: 1px solid {BORDER_COLOR};
                border-radius: 6px;
                margin-top: 8px;
                padding-top: 8px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: {PRIMARY_TEXT};
                background-color: {PRIMARY_BG};
            }}
        """)
        prediction_layout = QVBoxLayout(prediction_group)

        # Create a more prominent display for predictions
        predictions_container = QHBoxLayout()

        # Player 1 prediction (smaller, de-emphasized)
        player1_container = QVBoxLayout()
        self.player1_set_prob_label = QLabel("Player 1: N/A")
        self.player1_set_prob_label.setFont(QFont("Inter", 13, QFont.Normal))
        self.player1_set_prob_label.setAlignment(Qt.AlignCenter)
        player1_container.addWidget(self.player1_set_prob_label)

        self.player1_set_prob_bar = QProgressBar()
        self.player1_set_prob_bar.setMaximum(100)
        self.player1_set_prob_bar.setMinimumHeight(20)
        self.player1_set_prob_bar.setStyleSheet(f"""
            QProgressBar {{
                border: 1px solid {BORDER_COLOR};
                border-radius: 4px;
                text-align: center;
                font-weight: normal;
                font-size: 10px;
                background-color: {PRIMARY_BG};
                color: {PRIMARY_TEXT};
            }}
            QProgressBar::chunk {{
                background-color: #666666;
                border-radius: 3px;
            }}
        """)
        player1_container.addWidget(self.player1_set_prob_bar)

        # VS separator (smaller)
        vs_label = QLabel("VS")
        vs_label.setFont(QFont("Inter", 14, QFont.Normal))
        vs_label.setAlignment(Qt.AlignCenter)
        vs_label.setStyleSheet(f"color: {PRIMARY_TEXT}; margin: 0 15px;")

        # Player 2 prediction (smaller, de-emphasized)
        player2_container = QVBoxLayout()
        self.player2_set_prob_label = QLabel("Player 2: N/A")
        self.player2_set_prob_label.setFont(QFont("Inter", 13, QFont.Normal))
        self.player2_set_prob_label.setAlignment(Qt.AlignCenter)
        player2_container.addWidget(self.player2_set_prob_label)

        self.player2_set_prob_bar = QProgressBar()
        self.player2_set_prob_bar.setMaximum(100)
        self.player2_set_prob_bar.setMinimumHeight(20)
        self.player2_set_prob_bar.setStyleSheet(f"""
            QProgressBar {{
                border: 1px solid {BORDER_COLOR};
                border-radius: 4px;
                text-align: center;
                font-weight: normal;
                font-size: 10px;
                background-color: {PRIMARY_BG};
                color: {PRIMARY_TEXT};
            }}
            QProgressBar::chunk {{
                background-color: #666666;
                border-radius: 3px;
            }}
        """)
        player2_container.addWidget(self.player2_set_prob_bar)

        # Add to horizontal layout
        predictions_container.addLayout(player1_container)
        predictions_container.addWidget(vs_label)
        predictions_container.addLayout(player2_container)
        prediction_layout.addLayout(predictions_container)

        # Confidence (smaller, below predictions)
        self.set_confidence_label = QLabel("Confidence: N/A")
        self.set_confidence_label.setFont(QFont("Inter", 9))
        self.set_confidence_label.setAlignment(Qt.AlignCenter)
        self.set_confidence_label.setStyleSheet(f"color: {PRIMARY_TEXT}; margin-top: 8px;")
        prediction_layout.addWidget(self.set_confidence_label)

        layout.addWidget(prediction_group)

        # Betting recommendations section (expanded to use more space)
        scenarios_group = QGroupBox("💰 Betting Recommendations")
        scenarios_group.setMaximumHeight(180)  # Increased from 120 to 180
        scenarios_layout = QVBoxLayout(scenarios_group)

        self.set_scenarios_text = QTextEdit()
        self.set_scenarios_text.setReadOnly(True)
        self.set_scenarios_text.setMaximumHeight(140)  # Increased from 80 to 140
        self.set_scenarios_text.setFont(QFont("Inter", 10))  # Increased from 9 to 10
        self.set_scenarios_text.setPlaceholderText("Betting recommendations and mathematical scenarios will appear here...")
        scenarios_layout.addWidget(self.set_scenarios_text)

        layout.addWidget(scenarios_group)

        # Check if API key is already configured
        self.check_gemini_api_status()

        return widget

    def create_match_manager_tab(self):
        """Create the match manager tab for multi-match management"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Header with current session info
        header_group = QGroupBox("Current Session")
        header_layout = QVBoxLayout(header_group)

        self.current_session_label = QLabel("No active session")
        self.current_session_label.setFont(QFont("Inter", 12, QFont.Bold))
        header_layout.addWidget(self.current_session_label)

        # Quick actions for current session
        current_actions_layout = QHBoxLayout()

        self.new_session_button = QPushButton("New Session")
        self.new_session_button.clicked.connect(self.create_new_session)
        current_actions_layout.addWidget(self.new_session_button)

        self.update_session_button = QPushButton("Update Current")
        self.update_session_button.clicked.connect(self.update_current_session)
        self.update_session_button.setEnabled(False)
        current_actions_layout.addWidget(self.update_session_button)

        current_actions_layout.addStretch()
        header_layout.addLayout(current_actions_layout)
        layout.addWidget(header_group)

        # Match list
        list_group = QGroupBox("Saved Matches")
        list_layout = QVBoxLayout(list_group)

        # Match list table
        self.match_list_table = QTableWidget()
        self.match_list_table.setColumnCount(5)
        self.match_list_table.setHorizontalHeaderLabels([
            "Match", "Status", "Last Updated", "Actions", "Delete"
        ])

        # Set column widths with better sizing for action buttons
        header = self.match_list_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # Match name
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # Status
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # Last updated
        header.setSectionResizeMode(3, QHeaderView.Fixed)  # Actions - fixed width for consistent button sizing
        header.setSectionResizeMode(4, QHeaderView.Fixed)  # Delete - fixed width for consistent button sizing

        # Set fixed widths for action columns to ensure buttons fit properly
        header.resizeSection(3, 80)  # Load button column
        header.resizeSection(4, 80)  # Delete button column

        # Apply dark mode styling to the match list table
        self.apply_table_styling(self.match_list_table)

        # Set minimum row height to accommodate buttons properly
        self.match_list_table.verticalHeader().setDefaultSectionSize(35)
        self.match_list_table.verticalHeader().setVisible(False)  # Hide row numbers for cleaner look

        list_layout.addWidget(self.match_list_table)

        # Refresh button
        refresh_button = QPushButton("Refresh List")
        refresh_button.clicked.connect(self.refresh_match_list)
        list_layout.addWidget(refresh_button)

        layout.addWidget(list_group)

        # Instructions
        instructions_group = QGroupBox("Instructions")
        instructions_layout = QVBoxLayout(instructions_group)

        instructions_text = QLabel("""
        <b>Multi-Match Management:</b><br>
        1. <b>New Session:</b> Create a new match session<br>
        2. <b>Analyze:</b> Go to Data Input tab and analyze your match<br>
        3. <b>Save to Queue:</b> Save the analyzed match to your queue<br>
        4. <b>Load:</b> Click Load to restore any saved match<br>
        5. <b>Update:</b> Add new data to existing matches<br><br>
        <i>Perfect for monitoring multiple live matches simultaneously!</i>
        """)
        instructions_text.setWordWrap(True)
        instructions_text.setFont(QFont("Inter", 9))
        instructions_layout.addWidget(instructions_text)

        layout.addWidget(instructions_group)

        # Initialize the match list
        self.refresh_match_list()

        return widget

    def auto_generate_code(self, name_field, code_field):
        """
        Automatically generate player code from name.
        First tries to extract from score data, then falls back to surname-based generation.
        """
        name = name_field.text().strip()
        if not name:
            return

        # First try to extract codes from score data if available
        extracted_codes = self.extract_player_codes_from_data()
        if extracted_codes:
            # Determine which player this is
            if name_field == self.player1_name and extracted_codes[0]:
                code_field.setText(extracted_codes[0])
                return
            elif name_field == self.player2_name and len(extracted_codes) > 1 and extracted_codes[1]:
                code_field.setText(extracted_codes[1])
                return

        # Fall back to surname-based generation
        name_parts = name.split()
        if len(name_parts) >= 2:
            # Use the second name (surname)
            surname = name_parts[1]
            # Get first 3 letters and convert to uppercase
            code = surname[:3].upper()
            # Update the code field
            code_field.setText(code)

    def extract_player_codes_from_data(self):
        """
        Extract player codes from the pasted score data.
        Returns a list of [player1_code, player2_code] or None if extraction fails.
        """
        try:
            match_data = self.match_data.toPlainText().strip()
            if not match_data:
                return None

            lines = match_data.split('\n')
            found_codes = []

            # Look for player codes in various formats
            for line in lines:
                line = line.strip()

                # Skip empty lines
                if not line:
                    continue

                extracted_code = None

                # Pattern 1: Prefixed codes like "I.MON", "J.MON" (single letter + period + code)
                if len(line) >= 3 and '.' in line:
                    parts = line.split('.')
                    if len(parts) == 2 and len(parts[0]) == 1 and parts[0].isalpha():
                        # Extract the code part after the period
                        code_part = parts[1].strip()
                        if len(code_part) >= 2 and code_part.isalpha() and code_part.isupper():
                            # Keep the full prefixed code (e.g., "I.MON" instead of just "MON")
                            extracted_code = line

                # Pattern 2: Plain codes (2+ letters, all uppercase)
                elif len(line) >= 2 and line.isalpha() and line.isupper():
                    extracted_code = line

                # Validate and add the extracted code
                if extracted_code:
                    # Avoid common non-player codes and tennis terms
                    # IMPORTANT: BP and AD are tennis scoring terms, not player codes
                    excluded_codes = {'SET', 'WIN', 'END', 'ADV', 'ACE', 'UNF', 'NET', 'OUT', 'DBF',
                                    'TIED', 'GAME', 'MATCH', 'POINT', 'SERVE', 'RETURN', 'BREAK', 'HOLD',
                                    'BP', 'AD', 'DEUCE', 'LOVE', 'ALL', 'FAULT', 'LET', 'WINNER', 'ERROR'}

                    if extracted_code not in excluded_codes:
                        found_codes.append(extracted_code)
                        # Stop after finding 2 codes (allow duplicates for same player codes)
                        if len(found_codes) >= 2:
                            break

            # Return the first two codes found (Player 1, Player 2)
            if len(found_codes) >= 2:
                return found_codes[:2]
            elif len(found_codes) == 1:
                return [found_codes[0], None]
            else:
                return None

        except Exception as e:
            print(f"Error extracting player codes from data: {e}")
            return None

    def auto_detect_starting_server(self):
        """
        Auto-detect the starting server based on the Break Point Theory.
        Analyzes the first game to determine who started serving.
        """
        try:
            # Get the current match data
            match_data = self.match_data.toPlainText().strip()
            if not match_data:
                return

            # Get player codes
            player1_code = self.player1_code.text().strip()
            player2_code = self.player2_code.text().strip()

            if not player1_code or not player2_code:
                self.auto_detect_label.setText("🤖 Auto-detect: Need player codes")
                return

            # Detect starting server using Break Point Theory
            detection_result = self._detect_starting_server_from_data(match_data, player1_code, player2_code)

            if detection_result and detection_result['detected_server']:
                detected_server = detection_result['detected_server']
                has_issues = detection_result['has_issues']

                # Set the detected starting server
                if detected_server == player1_code:
                    self.set_starting_server(1)
                    print(f"🎾 Auto-detected starting server: {player1_code}")

                    if has_issues:
                        self.auto_detect_label.setText(f"❌ Failed to Auto Detect - {player1_code} serves first")
                        self.auto_detect_label.setStyleSheet("color: #FF4444; font-size: 10px; font-weight: bold;")
                    else:
                        self.auto_detect_label.setText(f"🤖 Auto-detected: {player1_code} serves first")
                        self.auto_detect_label.setStyleSheet("color: #7054DD; font-size: 10px;")

                elif detected_server == player2_code:
                    self.set_starting_server(2)
                    print(f"🎾 Auto-detected starting server: {player2_code}")

                    if has_issues:
                        self.auto_detect_label.setText(f"❌ Failed to Auto Detect - {player2_code} serves first")
                        self.auto_detect_label.setStyleSheet("color: #FF4444; font-size: 10px; font-weight: bold;")
                    else:
                        self.auto_detect_label.setText(f"🤖 Auto-detected: {player2_code} serves first")
                        self.auto_detect_label.setStyleSheet("color: #7054DD; font-size: 10px;")
            else:
                self.auto_detect_label.setText("🤖 Auto-detect: No data yet")
                self.auto_detect_label.setStyleSheet("color: #7054DD; font-size: 10px;")

        except Exception as e:
            print(f"Error auto-detecting starting server: {e}")

    def _detect_starting_server_from_data(self, match_data: str, player1_code: str, player2_code: str) -> dict:
        """
        Implement the Break Point Theory to detect starting server.

        Theory:
        1. Look at the first game result (who leads 1-0)
        2. Check if there was a Break Point (BP) in the first game
        3. If BP present: The player leading 1-0 broke serve → Other player started serving
        4. If no BP: The player leading 1-0 held serve → That player started serving

        Returns: dict with 'detected_server' and 'has_issues' keys, or None if cannot determine
        """
        try:
            lines = match_data.strip().split('\n')

            # Find the first game data
            first_game_data = self._extract_first_game_data(lines)
            if not first_game_data:
                return None

            first_game_winner = first_game_data['winner']
            has_break_point = first_game_data['has_bp']
            has_issues = first_game_data.get('has_issues', False)  # Check if there were detection issues

            if not first_game_winner:
                return None

            # Check for complex scenarios that might indicate detection issues
            game_data = first_game_data.get('game_data', [])
            ad_count = sum(1 for line in game_data if 'AD' in line)
            bp_count = sum(1 for line in game_data if 'BP' in line)

            # Mark as having issues only for truly complex and potentially embarrassing situations
            # Only alert for very complex scenarios: 4+ BPs with multiple ADs
            if bp_count >= 4 and ad_count >= 2:
                has_issues = True
                print(f"🔍 Very complex first game detected: {ad_count} ADs, {bp_count} BPs, {len(game_data)} total scores")
            elif bp_count >= 5 or (ad_count >= 5 and bp_count >= 3):
                has_issues = True
                print(f"🔍 Extremely complex first game detected: {ad_count} ADs, {bp_count} BPs, {len(game_data)} total scores")

            # Apply Break Point Theory
            if has_break_point:
                # BP present: Winner broke serve → Other player started serving
                if first_game_winner == player1_code:
                    starting_server = player2_code
                    print(f"🔍 BP detected in first game. {player1_code} broke {player2_code}'s serve → {player2_code} started serving")
                else:
                    starting_server = player1_code
                    print(f"🔍 BP detected in first game. {player2_code} broke {player1_code}'s serve → {player1_code} started serving")
            else:
                # No BP: Winner held serve → That player started serving
                starting_server = first_game_winner
                print(f"🔍 No BP in first game. {first_game_winner} held serve → {first_game_winner} started serving")

            print(f"🎾 Break Point Theory Result: Starting server = {starting_server}, Issues = {has_issues}")

            return {
                'detected_server': starting_server,
                'has_issues': has_issues
            }

        except Exception as e:
            print(f"Error in _detect_starting_server_from_data: {e}")
            return None

    def _analyze_bp_sequence(self, scores_after_bp):
        """
        Analyze the sequence of scores after a break point to determine if server held or was broken.

        Key insight: We need to look at the final outcome, not just the presence of AD.
        - BP → AD → 40: Advantage was lost, back to deuce
        - BP → AD → (end): Player with break point won
        - BP → 40 → (end): Server saved break point
        """
        if not scores_after_bp:
            return {'server_held': False, 'explanation': 'No scores after BP - break likely occurred'}

        # Remove non-score elements (like numbers that indicate set scores)
        tennis_scores = []
        for score in scores_after_bp:
            if score in ['0', '15', '30', '40', 'AD']:
                tennis_scores.append(score)
            elif score.isdigit():
                # Hit a set score number, stop analyzing
                break

        print(f"🔍 Tennis scores after BP: {tennis_scores}")

        if not tennis_scores:
            return {'server_held': False, 'explanation': 'No tennis scores after BP'}

        # Analyze the sequence
        if len(tennis_scores) == 1:
            # Only one score after BP
            if tennis_scores[0] == 'AD':
                return {'server_held': False, 'explanation': 'BP → AD (break point converted)'}
            elif tennis_scores[0] == '40':
                return {'server_held': True, 'explanation': 'BP → 40 (server saved break point)'}

        elif len(tennis_scores) >= 2:
            # Multiple scores after BP
            if tennis_scores[0] == 'AD' and tennis_scores[1] == '40':
                # BP → AD → 40: Advantage lost, back to deuce
                # Need to look at what happens next
                if len(tennis_scores) == 2:
                    return {'server_held': True, 'explanation': 'BP → AD → 40 (advantage lost, likely server held)'}
                else:
                    # Continue analyzing the rest of the sequence
                    remaining = tennis_scores[2:]
                    return self._analyze_bp_sequence(remaining)
            elif tennis_scores[0] == 'AD':
                # BP → AD → something else (not 40)
                return {'server_held': False, 'explanation': f'BP → AD → {tennis_scores[1]} (break point converted)'}
            elif tennis_scores[0] == '40':
                return {'server_held': True, 'explanation': 'BP → 40 (server saved break point)'}

        # Default case
        return {'server_held': False, 'explanation': f'Unclear sequence: {tennis_scores}'}

    def _determine_if_break_occurred(self, winner_code, bp_sequence, player1_code, player2_code):
        """
        Determine if a break occurred using enhanced logic that properly handles complex scenarios.

        Key insights:
        1. BP means the returner has a break point opportunity
        2. We need to track who actually won the game (winner_code) vs who had the break point
        3. AD after BP could mean either player got the advantage - context matters
        4. The final outcome determines if a break occurred, not intermediate scores

        Enhanced patterns:
        1. BP → 40: If winner matches returner, break occurred; if winner matches server, server saved BP
        2. BP → AD: Need to determine who got AD and if they won the game
        3. BP → AD → 40: Back to deuce, need to see final outcome
        4. Complex sequences: Analyze final winner vs expected server
        """

        if not bp_sequence:
            print(f"🔍 No sequence after BP - break likely occurred")
            return True

        # Clean the sequence - only tennis scores
        tennis_scores = [score for score in bp_sequence if score in ['0', '15', '30', '40', 'AD']]
        print(f"🔍 Clean BP sequence: {tennis_scores}")

        if not tennis_scores:
            print(f"🔍 No tennis scores after BP - break likely occurred")
            return True

        # Enhanced logic: Determine who should be serving based on game position
        # In the first game, we need to figure out who was serving
        # The winner_code tells us who won the game
        # If there was a BP, the returner had the break point opportunity

        # Simple case: No AD in sequence
        if 'AD' not in tennis_scores:
            # BP followed by regular scores - break likely occurred
            print(f"🔍 BP → {tennis_scores[0]} (no AD): Break likely occurred")
            return True

        # Complex case: AD is present - need sophisticated analysis
        return self._analyze_complex_bp_ad_sequence(tennis_scores, winner_code, player1_code, player2_code)

    def _analyze_complex_bp_ad_sequence(self, tennis_scores, winner_code, player1_code, player2_code):
        """
        Analyze complex sequences involving both BP and AD to determine if break occurred.

        Key insight: We need to determine who had the advantage and whether they won.
        Since we don't track individual point winners, we use positional logic and final outcome.
        """
        print(f"🔍 Analyzing complex BP+AD sequence: {tennis_scores}")

        # Count the complexity of the sequence
        bp_count = tennis_scores.count('BP') if 'BP' in tennis_scores else 0
        ad_count = tennis_scores.count('AD')
        total_scores = len(tennis_scores)

        print(f"🔍 Sequence complexity: {total_scores} scores, {ad_count} ADs, {bp_count} BPs")

        # If the sequence is extremely complex (many ADs), use simplified logic
        if ad_count >= 3 or total_scores >= 8:
            print(f"🔍 Complex sequence detected - using simplified heuristic")
            return self._use_simplified_break_heuristic(tennis_scores, winner_code, player1_code, player2_code)

        # Find all AD positions
        ad_positions = [i for i, score in enumerate(tennis_scores) if score == 'AD']
        print(f"🔍 AD positions: {ad_positions}")

        if not ad_positions:
            # No AD found (shouldn't happen as we checked before)
            return True

        # Analyze the final AD in the sequence
        final_ad_pos = ad_positions[-1]

        # If AD is at the very end, someone won with advantage
        if final_ad_pos == len(tennis_scores) - 1:
            print(f"🔍 Game ended with AD - someone won with advantage")
            # Use position-based logic: odd positions = player1, even = player2
            # If the final AD position suggests the returner won, it's a break
            return self._determine_break_from_final_ad_position(final_ad_pos, winner_code, player1_code, player2_code)

        # AD is not at the end - analyze what happened after
        scores_after_final_ad = tennis_scores[final_ad_pos + 1:]
        print(f"🔍 Scores after final AD: {scores_after_final_ad}")

        if not scores_after_final_ad:
            # AD was the last score - break likely occurred
            print(f"🔍 AD was final score - break likely occurred")
            return True

        # Check what happened after the final AD
        next_score = scores_after_final_ad[0]

        if next_score == '40':
            # AD → 40 means advantage was lost, back to deuce
            print(f"🔍 AD → 40: Advantage lost, back to deuce")
            # Continue analyzing the remaining sequence
            remaining_scores = scores_after_final_ad[1:]
            if not remaining_scores:
                # Sequence ended at deuce - inconclusive, use heuristic
                print(f"🔍 Sequence ended at deuce after AD → 40 - using heuristic")
                return len(tennis_scores) <= 4  # Short sequence = break, long = hold
            else:
                # Recursively analyze remaining sequence
                return self._analyze_complex_bp_ad_sequence(remaining_scores, winner_code, player1_code, player2_code)
        else:
            # AD followed by something other than 40 - break likely occurred
            print(f"🔍 AD → {next_score}: Break likely occurred")
            return True

    def _use_simplified_break_heuristic(self, tennis_scores, winner_code, player1_code, player2_code):
        """
        Simplified heuristic for very complex sequences with many ADs and BPs.

        When the sequence is too complex to analyze precisely, use these rules:
        1. If sequence is very long (8+ scores), server likely held (fought back)
        2. If sequence ends with AD, check if it's likely server or returner
        3. Default to break occurred for ambiguous cases (conservative approach)
        """
        total_scores = len(tennis_scores)
        ad_count = tennis_scores.count('AD')

        print(f"🔍 Using simplified heuristic: {total_scores} scores, {ad_count} ADs")

        # Very long sequences suggest server fought back and held
        if total_scores >= 10:
            print(f"🔍 Very long sequence ({total_scores}) - server likely held")
            return False

        # If sequence ends with AD, use position logic
        if tennis_scores[-1] == 'AD':
            final_ad_pos = len(tennis_scores) - 1
            return self._determine_break_from_final_ad_position(final_ad_pos, winner_code, player1_code, player2_code)

        # For moderately complex sequences, analyze the pattern
        # Key insight: AD → 40 suggests server fought back and likely held
        if 'AD' in tennis_scores and '40' in tennis_scores:
            ad_positions = [i for i, score in enumerate(tennis_scores) if score == 'AD']
            for ad_pos in ad_positions:
                if ad_pos + 1 < len(tennis_scores) and tennis_scores[ad_pos + 1] == '40':
                    print(f"🔍 Found AD → 40 pattern: Server fought back, likely held serve")
                    return False

        # If sequence is moderately long (6+ scores), server likely held
        if total_scores >= 6:
            print(f"🔍 Moderately long sequence ({total_scores}) - server likely held")
            return False

        # Short complex sequences - break more likely
        print(f"🔍 Short complex sequence - break likely occurred")
        return True

    def _determine_break_from_final_ad_position(self, ad_position, winner_code, player1_code, player2_code):
        """
        Determine if a break occurred based on the position of the final AD.

        This uses the assumption that scores alternate between players.
        Position 0, 2, 4... = Player 1
        Position 1, 3, 5... = Player 2
        """
        # Determine which player likely had the final advantage
        player_with_ad = player1_code if ad_position % 2 == 0 else player2_code

        print(f"🔍 Final AD at position {ad_position} suggests {player_with_ad} had advantage")

        # If the winner matches the player who had advantage, they won
        # In the context of a BP, if the returner won, it's a break
        # We need to determine who was serving in the first game
        # Since this is the first game, we can use a simple heuristic:
        # If the game was complex (many ADs), the server likely held
        if player_with_ad == winner_code:
            print(f"🔍 Player with final AD ({player_with_ad}) won the game")
            # This could be either a break or hold - use length heuristic
            return ad_position <= 3  # Short sequence = break, longer = hold
        else:
            print(f"🔍 Player with final AD ({player_with_ad}) did not win - unusual scenario")
            return True  # Conservative: assume break occurred

    def _extract_first_game_data(self, lines: list) -> dict:
        """
        Extract data from the first game to determine winner and break point presence.

        Returns: dict with 'winner', 'has_bp', 'winner_name', 'game_data', and 'has_issues' keys
        """
        try:
            i = 0
            while i < len(lines):
                line = lines[i].strip()

                # Skip empty lines and comments
                if not line or line.startswith('#'):
                    i += 1
                    continue

                # Look for the first game pattern: "1-0" or "0-1"
                if (i + 2 < len(lines) and
                    lines[i+1].strip() == '-' and
                    lines[i].strip().isdigit() and
                    lines[i+2].strip().isdigit()):

                    score1 = int(lines[i].strip())
                    score2 = int(lines[i+2].strip())

                    # Check if this is the first game (1-0 or 0-1)
                    if (score1 == 1 and score2 == 0) or (score1 == 0 and score2 == 1):
                        # Found first game, now extract the winner and check for BP
                        i += 3  # Move past the score

                        # Get the winner name (next line after score)
                        winner_name = None
                        if i < len(lines):
                            potential_winner = lines[i].strip()
                            # Check if this looks like a player name (contains letters and possibly spaces)
                            if any(char.isalpha() for char in potential_winner):
                                winner_name = potential_winner
                                i += 1

                        # Extract game data - go back to find the point-by-point data
                        # The game data should be between "tied" and the score
                        game_data_lines = []

                        # Go back to find the start of this game (look for "tied")
                        game_start = i - 3  # Start from before the score
                        while game_start >= 0:
                            if lines[game_start].strip().lower() == 'tied':
                                break
                            game_start -= 1

                        # Extract all lines from after "tied" until the score
                        if game_start >= 0:
                            for j in range(game_start + 1, i - 3):  # i-3 is where the score starts
                                if j < len(lines):
                                    line = lines[j].strip()
                                    # Skip player codes but include all other data
                                    if line and not (len(line) == 3 and line.isupper()):
                                        game_data_lines.append(line)

                        # Use existing robust game analysis system
                        winner_result = self._determine_winner_using_existing_system(
                            game_data_lines, score1, score2, winner_name
                        )
                        winner_code = winner_result['winner_code']
                        has_issues = winner_result['has_issues']

                        # Enhanced Break Point Analysis
                        # Check if there was a break point and determine if break occurred
                        has_bp = False
                        break_occurred = False

                        # Find all BP occurrences and analyze using enhanced logic
                        for i, game_line in enumerate(game_data_lines):
                            if game_line == 'BP' or 'BP' in game_line.split() or 'BP' in game_line:
                                has_bp = True
                                print(f"🔍 Found BP at index {i} in first game: '{game_line}'")

                                # Get the sequence after BP
                                remaining_scores = game_data_lines[i + 1:]
                                print(f"🔍 Scores after BP: {remaining_scores}")

                                # Get player codes from the class instance
                                player1_code = self._get_player_code_from_position(1)
                                player2_code = self._get_player_code_from_position(2)

                                # Use enhanced break analysis
                                break_occurred = self._determine_if_break_occurred(winner_code, remaining_scores, player1_code, player2_code)

                                print(f"🔍 Enhanced break analysis: Winner={winner_code}, BP_sequence={remaining_scores}, Break_occurred={break_occurred}")
                                break  # Found first BP, no need to continue

                        print(f"🔍 First game analysis: Winner={winner_code}, Winner_name='{winner_name}', BP={has_bp}, Break_occurred={break_occurred}, Data lines={len(game_data_lines)}")

                        return {
                            'winner': winner_code,
                            'has_bp': break_occurred,  # Only True if break actually occurred
                            'winner_name': winner_name,
                            'game_data': game_data_lines,
                            'has_issues': has_issues  # True if there were detection issues
                        }

                i += 1

            print("🔍 No first game pattern found in data")
            return None

        except Exception as e:
            print(f"Error extracting first game data: {e}")
            return None

    def _get_player_code_from_position(self, position: int) -> str:
        """Get player code based on position (1 or 2)"""
        if position == 1:
            return self.player1_code.text().strip()
        else:
            return self.player2_code.text().strip()

    def _determine_winner_using_existing_system(self, game_data_lines: list, score1: int, score2: int, winner_name: str) -> dict:
        """
        Simple and reliable winner determination using set score and winner name.
        Focus on the Break Point Theory without overcomplicating.

        Returns: dict with 'winner_code' and 'has_issues' keys
        """
        try:
            player1_code = self._get_player_code_from_position(1)
            player2_code = self._get_player_code_from_position(2)

            print(f"🔍 Simple analysis: Set score {score1}-{score2}, Winner name: '{winner_name}'")

            # Initialize issue tracking
            has_issues = False

            # Step 1: Determine winner from set score and winner name
            if score1 == 1 and score2 == 0:
                # Player 1 position won, but validate with winner name
                if winner_name:
                    # Check if winner name clearly indicates a specific player
                    if player2_code in winner_name or self._name_contains_player(winner_name, player2_code):
                        winner_code = player2_code
                        print(f"🔍 Winner name '{winner_name}' indicates {player2_code}")
                    elif player1_code in winner_name or self._name_contains_player(winner_name, player1_code):
                        winner_code = player1_code
                        print(f"🔍 Winner name '{winner_name}' indicates {player1_code}")
                    else:
                        # Fallback to position - this indicates an issue
                        winner_code = player1_code
                        has_issues = True
                        print(f"🔍 Winner name unclear, using position → {player1_code}")
                else:
                    winner_code = player1_code
                    print(f"🔍 No winner name, using position → {player1_code}")
            elif score1 == 0 and score2 == 1:
                # Player 2 position won, but validate with winner name
                if winner_name:
                    if player1_code in winner_name or self._name_contains_player(winner_name, player1_code):
                        winner_code = player1_code
                        print(f"🔍 Winner name '{winner_name}' indicates {player1_code}")
                    elif player2_code in winner_name or self._name_contains_player(winner_name, player2_code):
                        winner_code = player2_code
                        print(f"🔍 Winner name '{winner_name}' indicates {player2_code}")
                    else:
                        winner_code = player2_code
                        has_issues = True
                        print(f"🔍 Winner name unclear, using position → {player2_code}")
                else:
                    winner_code = player2_code
                    print(f"🔍 No winner name, using position → {player2_code}")
            else:
                # Fallback
                winner_code = player1_code
                print(f"🔍 Fallback → {player1_code}")

            print(f"🔍 Determined first game winner: {winner_code}")
            return {
                'winner_code': winner_code,
                'has_issues': has_issues
            }

        except Exception as e:
            print(f"Error in simple analysis: {e}")
            # Ultimate fallback
            player1_code = self._get_player_code_from_position(1)
            return {
                'winner_code': player1_code,
                'has_issues': True  # Exception indicates an issue
            }

    def _name_contains_player(self, winner_name: str, player_code: str) -> bool:
        """Enhanced name matching using GUI player names and Players database"""
        if not winner_name or not player_code:
            return False

        name_lower = winner_name.lower()

        # First, check against the actual player names entered in GUI fields
        player1_code = self.player1_code.text().strip()
        player2_code = self.player2_code.text().strip()

        if player_code == player1_code:
            player_name = self.player1_name.text().strip()
        elif player_code == player2_code:
            player_name = self.player2_name.text().strip()
        else:
            player_name = ""

        # Check direct name matching with GUI player names
        if player_name:
            player_name_lower = player_name.lower()
            player_name_parts = player_name_lower.split()

            # Check if any meaningful part of the player name is in the winner name
            for part in player_name_parts:
                if len(part) > 2 and part in name_lower:
                    print(f"🔍 Name match found: '{part}' from player '{player_name}' in winner '{winner_name}'")
                    return True

            # Also check reverse - if any part of winner name is in player name
            winner_parts = name_lower.split()
            for part in winner_parts:
                if len(part) > 2 and part in player_name_lower:
                    print(f"🔍 Reverse name match found: '{part}' from winner '{winner_name}' in player '{player_name}'")
                    return True

        # Fallback to dynamic mappings from Players folder
        mappings = self._get_player_mappings()

        # Check if player code has a mapping
        if player_code in mappings:
            mapped_name = mappings[player_code].lower()
            # Check if any part of the mapped name is in the winner name
            name_parts = mapped_name.split()
            for part in name_parts:
                if len(part) > 2 and part in name_lower:  # Only check meaningful parts
                    print(f"🔍 Mapping match found: '{part}' from mapping '{mapped_name}' in winner '{winner_name}'")
                    return True

        return False

    def _get_player_mappings(self):
        """Build player code to name mappings from Players folder"""
        if hasattr(self, '_cached_player_mappings'):
            return self._cached_player_mappings

        mappings = {}

        try:
            import os
            players_folder = "Players"

            if os.path.exists(players_folder):
                for filename in os.listdir(players_folder):
                    if filename.endswith('.txt'):
                        # Extract player name from filename (remove .txt)
                        player_name = filename[:-4]

                        # Generate player code from name
                        player_code = self._generate_player_code(player_name)

                        if player_code:
                            mappings[player_code] = player_name

                print(f"🔍 Loaded {len(mappings)} player mappings from Players folder")
            else:
                print("🔍 Players folder not found, using fallback mappings")
                # Fallback to basic mappings if folder doesn't exist
                mappings = {
                    'ALC': 'Carlos Alcaraz',
                    'MUS': 'Lorenzo Musetti',
                    'PAU': 'Tommy Paul',
                    'SIN': 'Jannik Sinner',
                    'ZVE': 'Alexander Zverev',
                    'RUU': 'Casper Ruud',
                    'HUR': 'Hubert Hurkacz'
                }

        except Exception as e:
            print(f"Error loading player mappings: {e}")
            mappings = {}

        # Cache the mappings
        self._cached_player_mappings = mappings
        return mappings

    def _generate_player_code(self, player_name: str) -> str:
        """Generate a 3-letter player code from player name"""
        if not player_name:
            return ""

        # Split name into parts
        name_parts = player_name.split()

        if len(name_parts) >= 2:
            # Use first 3 letters of last name
            last_name = name_parts[-1]
            if len(last_name) >= 3:
                return last_name[:3].upper()

        # Fallback: use first 3 letters of full name (no spaces)
        clean_name = player_name.replace(" ", "")
        if len(clean_name) >= 3:
            return clean_name[:3].upper()

        return ""

    def auto_extract_codes_from_data(self):
        """
        Automatically extract and set player codes when match data is pasted.
        Only updates codes if they are currently empty or auto-generated.
        """
        try:
            extracted_codes = self.extract_player_codes_from_data()
            if not extracted_codes:
                return

            # Only update if current codes are empty or seem auto-generated
            current_p1_code = self.player1_code.text().strip()
            current_p2_code = self.player2_code.text().strip()

            codes_updated = False

            # Update Player 1 code if available and current is empty or different
            if extracted_codes[0] and (not current_p1_code or current_p1_code != extracted_codes[0]):
                self.player1_code.setText(extracted_codes[0])
                codes_updated = True

            # Update Player 2 code if available and current is empty or different
            if len(extracted_codes) > 1 and extracted_codes[1] and (not current_p2_code or current_p2_code != extracted_codes[1]):
                self.player2_code.setText(extracted_codes[1])
                codes_updated = True

            # Update serving combo and previous set winners if codes were updated
            if codes_updated:
                self.update_serving_combo()

        except Exception as e:
            print(f"Error in auto-extracting codes: {e}")

    def set_favorite(self, player_num):
        """
        Set the favorite player and enable/disable odds input.
        player_num: 0 = No Favorite, 1 = Player 1, 2 = Player 2
        """
        # Uncheck all buttons first
        self.player1_favorite_btn.setChecked(False)
        self.player2_favorite_btn.setChecked(False)
        self.no_favorite_btn.setChecked(False)

        # Check the selected button
        if player_num == 1:
            self.player1_favorite_btn.setChecked(True)
            self.favorite_odds.setEnabled(True)
            self.favorite_odds.setPlaceholderText("e.g., 1.45")
        elif player_num == 2:
            self.player2_favorite_btn.setChecked(True)
            self.favorite_odds.setEnabled(True)
            self.favorite_odds.setPlaceholderText("e.g., 1.45")
        else:  # No favorite
            self.no_favorite_btn.setChecked(True)
            self.favorite_odds.setEnabled(False)
            self.favorite_odds.clear()
            self.favorite_odds.setPlaceholderText("No favorite selected")

    def get_favorite_info(self):
        """
        Get the current favorite player and odds.
        Returns: (favorite_code, favorite_odds)
        """
        favorite = None
        favorite_odds = None

        if self.player1_favorite_btn.isChecked():
            favorite = self.player1_code.text().strip()
        elif self.player2_favorite_btn.isChecked():
            favorite = self.player2_code.text().strip()

        if favorite and self.favorite_odds.text().strip():
            try:
                favorite_odds = float(self.favorite_odds.text().strip())
            except ValueError:
                pass

        return favorite, favorite_odds

    def set_starting_server(self, server_num, manual_override=False):
        """
        Set the starting server selection.
        server_num: 1 = Player 1, 2 = Player 2
        manual_override: True if user manually selected (disables auto-detection feedback)
        """
        # Uncheck all buttons first
        self.player1_server_btn.setChecked(False)
        self.player2_server_btn.setChecked(False)

        # Check the selected button
        if server_num == 1:
            self.player1_server_btn.setChecked(True)
            if manual_override:
                self.auto_detect_label.setText("🤖 Manual override: Player 1 serves")
                self.auto_detect_label.setStyleSheet("color: #7054DD; font-size: 10px;")  # Reset to normal color
        elif server_num == 2:
            self.player2_server_btn.setChecked(True)
            if manual_override:
                self.auto_detect_label.setText("🤖 Manual override: Player 2 serves")
                self.auto_detect_label.setStyleSheet("color: #7054DD; font-size: 10px;")  # Reset to normal color
        else:  # Default to Player 1
            self.player1_server_btn.setChecked(True)
            if manual_override:
                self.auto_detect_label.setText("🤖 Manual override: Player 1 serves")
                self.auto_detect_label.setStyleSheet("color: #7054DD; font-size: 10px;")  # Reset to normal color

    def get_starting_server(self):
        """
        Get the current starting server selection.
        Returns: player code of the selected starting server
        """
        if self.player1_server_btn.isChecked():
            return self.player1_code.text().strip()
        elif self.player2_server_btn.isChecked():
            return self.player2_code.text().strip()
        else:  # Default to Player 1 if somehow nothing is selected
            return self.player1_code.text().strip()

    def create_new_session(self):
        """Create a new match session"""
        from PyQt5.QtWidgets import QInputDialog

        match_name, ok = QInputDialog.getText(
            self, 'New Match Session',
            'Enter match name (e.g., "Djokovic vs Federer"):'
        )

        if ok and match_name.strip():
            session_id = self.match_manager.create_session(match_name.strip())
            self.current_session_id = session_id
            self.match_manager.active_session_id = session_id
            self.update_current_session_display()
            self.refresh_match_list()

            # Clear current data for new session
            self.clear_all()

            QMessageBox.information(
                self, "Session Created",
                f"New session '{match_name}' created successfully!\n"
                "You can now enter match data and analyze."
            )

    def update_current_session(self):
        """Update the current session with latest data"""
        if not self.current_session_id:
            QMessageBox.warning(self, "No Session", "No active session to update.")
            return

        gui_data = self.get_current_gui_data()
        success = self.match_manager.save_session_from_gui(self.current_session_id, gui_data)

        if success:
            self.update_current_session_display()
            self.refresh_match_list()
            QMessageBox.information(self, "Session Updated", "Current session updated successfully!")
        else:
            QMessageBox.critical(self, "Update Failed", "Failed to update session.")

    def save_to_queue(self):
        """Save current analysis to match queue"""
        if not hasattr(self, 'predictor') or not self.predictor.game_analyses:
            QMessageBox.warning(
                self, "No Analysis",
                "Please analyze a match first before saving to queue."
            )
            return

        # Create new session if none exists
        if not self.current_session_id:
            player1 = self.player1_name.text().strip()
            player2 = self.player2_name.text().strip()
            match_name = f"{player1} vs {player2}" if player1 and player2 else "Unnamed Match"
            self.current_session_id = self.match_manager.create_session(match_name)

        # Save current state
        gui_data = self.get_current_gui_data()
        success = self.match_manager.save_session_from_gui(self.current_session_id, gui_data)

        if success:
            self.update_current_session_display()
            self.refresh_match_list()

            # Ask if user wants to clear for new match
            reply = QMessageBox.question(
                self, "Match Saved",
                "Match saved to queue successfully!\n\n"
                "Would you like to clear all fields for a new match analysis?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )

            if reply == QMessageBox.Yes:
                self.clear_all()
                self.current_session_id = None
                self.match_manager.active_session_id = None
                self.update_current_session_display()
        else:
            QMessageBox.critical(self, "Save Failed", "Failed to save match to queue.")

    def save_draft_to_queue(self):
        """Save current match data to queue as draft (without requiring analysis)"""
        # Validate minimum required fields
        player1 = self.player1_name.text().strip()
        player2 = self.player2_name.text().strip()

        if not player1 or not player2:
            QMessageBox.warning(
                self, "Missing Information",
                "Please enter both player names before saving to queue."
            )
            return

        # Create new session if none exists
        if not self.current_session_id:
            match_name = f"{player1} vs {player2}"
            self.current_session_id = self.match_manager.create_session(match_name)

        # Save current state as draft
        gui_data = self.get_current_gui_data()
        gui_data['is_draft'] = True  # Mark as draft
        gui_data['created_from_monitor'] = getattr(self, '_from_monitor', False)

        success = self.match_manager.save_session_from_gui(self.current_session_id, gui_data)

        if success:
            self.update_current_session_display()
            self.refresh_match_list()

            # Show 1-second auto-close notification
            self.show_auto_close_notification("Draft Saved", "Match draft saved to queue successfully!")

            # Automatically clear all fields for new match
            self.clear_all()
            self.current_session_id = None
            self.match_manager.active_session_id = None
            self.update_current_session_display()
        else:
            QMessageBox.critical(self, "Save Failed", "Failed to save draft to queue.")

    def import_match_from_monitor(self, match_data: dict):
        """Import match data from live monitor and populate GUI fields"""
        try:
            match_info = match_data.get('match_info', {})

            # Populate player information
            self.player1_name.setText(match_info.get('player1_name', ''))
            self.player2_name.setText(match_info.get('player2_name', ''))
            self.player1_code.setText(match_info.get('player1_code', ''))
            self.player2_code.setText(match_info.get('player2_code', ''))

            # Set match format to Best of 3 (default for Challenger/ATP)
            self.match_format.setCurrentText("Best of 3")

            # Set current set to Set 1 (default for new matches)
            self.set_number.setCurrentIndex(0)  # Set 1

            # Set surface to Hard (most common)
            if hasattr(self, 'surface_type'):
                self.surface_type.setCurrentText("Hard")

            # Set starting server to Player 1 (default)
            self.set_starting_server(1)

            # Mark this as imported from monitor
            self._from_monitor = True

            # Add tournament info to match data as a comment
            tournament = match_info.get('tournament', '')
            current_score = match_info.get('current_score', '')
            import_note = f"# Imported from Live Monitor\n# Tournament: {tournament}\n# Score when imported: {current_score}\n# Import time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"

            self.match_data.setPlainText(import_note)

            # Automatically save as draft to queue
            self.save_draft_to_queue()

            return True

        except Exception as e:
            QMessageBox.critical(
                self, "Import Error",
                f"Failed to import match data from monitor: {str(e)}"
            )
            return False

    def _load_monitor_state(self):
        """Load monitor state from persistent storage"""
        try:
            from pathlib import Path
            state_file = Path("monitor_state.json")
            if state_file.exists():
                with open(state_file, 'r') as f:
                    state = json.load(f)
                    self._last_processed_timestamp = state.get('last_processed_timestamp')
            else:
                self._last_processed_timestamp = None
        except Exception:
            self._last_processed_timestamp = None

    def _save_monitor_state(self):
        """Save monitor state to persistent storage"""
        try:
            from pathlib import Path
            state_file = Path("monitor_state.json")
            state = {
                'last_processed_timestamp': self._last_processed_timestamp,
                'last_updated': datetime.now().isoformat()
            }
            with open(state_file, 'w') as f:
                json.dump(state, f, indent=2)
        except Exception as e:
            print(f"Warning: Could not save monitor state: {e}")

    def _save_adaptive_learning_state(self):
        """Save adaptive learning system state"""
        try:
            # The adaptive learning system automatically saves its state
            # when configurations are changed, but we can trigger a final save here
            from adaptive_learning_system import AdaptiveLearningSystem
            from prediction_weights_manager import PredictionWeightsManager

            learning_system = AdaptiveLearningSystem()
            weights_manager = PredictionWeightsManager()

            # Ensure all configurations are saved
            learning_system.save_configurations()
            weights_manager._save_manager_config()

            print("✅ Adaptive learning state saved successfully")
        except Exception as e:
            print(f"Warning: Could not save adaptive learning state: {e}")

    def check_for_monitor_data(self):
        """Check for new match data from live monitor"""
        try:
            from pathlib import Path
            bridge_file = Path("monitor_bridge_data.json")

            if bridge_file.exists():
                with open(bridge_file, 'r') as f:
                    bridge_data = json.load(f)

                # Check if this is new data (not already processed)
                timestamp = bridge_data.get('timestamp', '')
                if self._last_processed_timestamp and timestamp == self._last_processed_timestamp:
                    return  # Already processed this data

                # Check if match already exists to prevent duplicates
                match_info = bridge_data.get('match_info', {})
                player1 = match_info.get('player1_name', '')
                player2 = match_info.get('player2_name', '')

                if self.match_already_exists(player1, player2):
                    self._last_processed_timestamp = timestamp
                    self._save_monitor_state()
                    return  # Match already exists, don't create duplicate

                # Check if bridge data is too old (more than 24 hours)
                try:
                    from datetime import datetime, timedelta
                    bridge_time = datetime.fromisoformat(timestamp.replace('Z', '+00:00').replace('+00:00', ''))
                    if datetime.now() - bridge_time > timedelta(hours=24):
                        print(f"Ignoring old bridge data from {bridge_time}")
                        self._last_processed_timestamp = timestamp
                        self._save_monitor_state()
                        # Optionally clean up old bridge file
                        bridge_file.unlink()
                        return
                except Exception:
                    pass  # If timestamp parsing fails, continue with normal processing

                # Import the match data (automatically creates draft)
                if self.import_match_from_monitor(bridge_data):
                    self._last_processed_timestamp = timestamp
                    self._save_monitor_state()
                    # No popup notification - silent operation

        except Exception as e:
            # Silently handle errors to avoid disrupting normal operation
            pass

    def match_already_exists(self, player1: str, player2: str) -> bool:
        """Check if a match between these players already exists in the queue"""
        try:
            for session_id, session in self.match_manager.sessions.items():
                # Check both player name combinations
                if ((session.player1_name == player1 and session.player2_name == player2) or
                    (session.player1_name == player2 and session.player2_name == player1)):
                    return True
            return False
        except Exception:
            return False

    def get_current_gui_data(self) -> dict:
        """Extract current GUI data for session saving"""
        # Get favorite info
        favorite_player = 0
        if self.player1_favorite_btn.isChecked():
            favorite_player = 1
        elif self.player2_favorite_btn.isChecked():
            favorite_player = 2

        favorite_odds = None
        if self.favorite_odds.text().strip():
            try:
                favorite_odds = float(self.favorite_odds.text().strip())
            except ValueError:
                pass

        # Get previous sets
        previous_sets = []
        current_set_idx = self.set_number.currentIndex()
        for i in range(current_set_idx):
            _, combo = self.prev_set_winners[i]
            winner_name = combo.currentText()
            if winner_name != "-":
                winner_code = self.get_code_from_full_name(winner_name)
                if winner_code:
                    previous_sets.append(winner_code)

        # Get current analysis data
        games_analyzed = len(self.predictor.game_analyses) if hasattr(self, 'predictor') and self.predictor.game_analyses else 0

        current_score = None
        if hasattr(self, 'predictor') and self.predictor.game_analyses:
            p1_games = sum(1 for g in self.predictor.game_analyses if g.winner == self.player1_code.text().strip())
            p2_games = sum(1 for g in self.predictor.game_analyses if g.winner == self.player2_code.text().strip())
            current_score = (p1_games, p2_games)

        return {
            'player1_name': self.player1_name.text().strip(),
            'player1_code': self.player1_code.text().strip(),
            'player2_name': self.player2_name.text().strip(),
            'player2_code': self.player2_code.text().strip(),
            'match_format': self.match_format.currentText(),
            'current_set': self.set_number.currentIndex() + 1,
            'surface': self.surface_type.currentText(),
            'starting_server': self.get_starting_server(),
            'favorite_player': favorite_player,
            'favorite_odds': favorite_odds,
            'previous_sets': previous_sets,
            'point_by_point_data': self.match_data.toPlainText(),
            'games_analyzed': games_analyzed,
            'current_score': current_score,
            'tournament_level': self.tournament_level.currentText(),
            'tournament_name': self.tournament_name.text().strip(),
            'prediction_history': []  # TODO: Implement prediction history serialization
        }

    def on_set_selection_changed(self, set_index):
        """
        Handle when a set winner is manually changed.
        Remove from auto-filled tracking and trigger auto-fill logic.
        """
        # If this set was auto-filled and now manually changed, remove from auto-filled tracking
        if set_index in self.auto_filled_sets:
            self.auto_filled_sets.discard(set_index)

        # Trigger auto-fill logic
        self.auto_fill_sets()

    def refresh_match_list(self):
        """Refresh the match list table"""
        sessions = self.match_manager.get_session_list()
        self.match_list_table.setRowCount(len(sessions))

        for row, session in enumerate(sessions):
            # Match name
            self.match_list_table.setItem(row, 0, QTableWidgetItem(session['display_name']))

            # Status
            status_item = QTableWidgetItem(session['status'])
            if session['is_active']:
                status_item.setBackground(QColor(112, 84, 221))  # Purple for active
            elif session['is_completed']:
                status_item.setBackground(QColor(132, 99, 255))  # Light purple for completed
            self.match_list_table.setItem(row, 1, status_item)

            # Last updated
            try:
                from datetime import datetime
                dt = datetime.fromisoformat(session['last_updated'])
                time_str = dt.strftime("%m/%d %H:%M")
            except:
                time_str = "Unknown"
            self.match_list_table.setItem(row, 2, QTableWidgetItem(time_str))

            # Load button with consistent styling
            load_button = QPushButton("Load")
            load_button.clicked.connect(lambda checked, sid=session['id']: self.load_session(sid))
            load_button.setStyleSheet(f"""
                QPushButton {{
                    background-color: {SUCCESS_COLOR};
                    color: {PRIMARY_TEXT};
                    border: none;
                    border-radius: 4px;
                    padding: 6px 12px;
                    font-weight: bold;
                    font-size: 11px;
                    font-family: 'Inter', sans-serif;
                    min-height: 20px;
                }}
                QPushButton:hover {{
                    background-color: #9575FF;
                    border: 1px solid #9575FF;
                }}
                QPushButton:pressed {{
                    background-color: #5A3FBB;
                }}
            """)
            self.match_list_table.setCellWidget(row, 3, load_button)

            # Delete button with consistent styling
            delete_button = QPushButton("Delete")
            delete_button.clicked.connect(lambda checked, sid=session['id']: self.delete_session(sid))
            delete_button.setStyleSheet(f"""
                QPushButton {{
                    background-color: {ERROR_COLOR};
                    color: {PRIMARY_TEXT};
                    border: none;
                    border-radius: 4px;
                    padding: 6px 12px;
                    font-weight: bold;
                    font-size: 11px;
                    font-family: 'Inter', sans-serif;
                    min-height: 20px;
                }}
                QPushButton:hover {{
                    background-color: #FF6666;
                    border: 1px solid #FF6666;
                }}
                QPushButton:pressed {{
                    background-color: #CC3333;
                }}
            """)
            self.match_list_table.setCellWidget(row, 4, delete_button)

    def load_session(self, session_id: str):
        """Load a saved session"""
        session_data = self.match_manager.load_session_to_gui(session_id)

        if not session_data:
            QMessageBox.critical(self, "Load Failed", "Failed to load session.")
            return

        # Use the centralized restore method
        self.restore_session_data(session_data)

        # Set current session
        self.current_session_id = session_id
        self.update_current_session_display()

        # If there's data, analyze it silently (without creating predictions)
        if session_data['point_by_point_data'].strip():
            self.auto_analysis_mode = True  # Enable auto-analysis mode
            try:
                self.analyze_match_silent()
            finally:
                self.auto_analysis_mode = False  # Always reset the flag

        # No popup notification - silent loading

        # Switch to Data Input tab
        self.tabs.setCurrentIndex(0)

    def delete_session(self, session_id: str):
        """Delete a session with confirmation"""
        reply = QMessageBox.question(
            self, "Delete Session",
            "Are you sure you want to delete this session?\n"
            "This action cannot be undone.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            success = self.match_manager.delete_session(session_id)
            if success:
                if self.current_session_id == session_id:
                    self.current_session_id = None
                    self.update_current_session_display()
                self.refresh_match_list()
                QMessageBox.information(self, "Session Deleted", "Session deleted successfully.")
            else:
                QMessageBox.critical(self, "Delete Failed", "Failed to delete session.")

    def update_current_session_display(self):
        """Update the current session display"""
        if self.current_session_id:
            session = self.match_manager.sessions.get(self.current_session_id)
            if session:
                self.current_session_label.setText(f"Active: {session.get_display_name()}")
                self.update_session_button.setEnabled(True)
            else:
                self.current_session_label.setText("No active session")
                self.update_session_button.setEnabled(False)
        else:
            self.current_session_label.setText("No active session")
            self.update_session_button.setEnabled(False)

    def get_code_from_full_name(self, full_name):
        """Convert full name to player code"""
        p1_name = self.player1_name.text().strip()
        p2_name = self.player2_name.text().strip()

        if full_name == p1_name:
            return self.player1_code.text().strip()
        elif full_name == p2_name:
            return self.player2_code.text().strip()
        return None

    def get_full_name_from_code(self, code):
        """Convert player code to full name"""
        p1_code = self.player1_code.text().strip()
        p2_code = self.player2_code.text().strip()
        p1_name = self.player1_name.text().strip()
        p2_name = self.player2_name.text().strip()

        if code == p1_code and p1_name:
            return p1_name
        elif code == p2_code and p2_name:
            return p2_name
        return None

    def auto_fill_sets(self):
        """
        Automatically fill set winners based on current set and manually selected winners.
        Only considers manually selected sets, ignoring auto-filled ones.
        """
        current_set_idx = self.set_number.currentIndex()
        match_format = self.match_format.currentText()

        if current_set_idx == 0:  # Set 1, no previous sets
            return

        # Get player names
        p1_name = self.player1_name.text().strip()
        p2_name = self.player2_name.text().strip()

        if not p1_name or not p2_name:
            return

        # Clear all auto-filled sets first
        for i in list(self.auto_filled_sets):
            if i < current_set_idx:
                _, combo = self.prev_set_winners[i]
                combo.setCurrentText("-")
                self.auto_filled_sets.discard(i)

        # Count wins for each player from MANUALLY set previous sets only
        p1_manual_wins = 0
        p2_manual_wins = 0
        manual_set_indices = []

        for i in range(current_set_idx):
            if i not in self.auto_filled_sets:  # Only count manual selections
                _, combo = self.prev_set_winners[i]
                winner = combo.currentText()
                if winner == p1_name:
                    p1_manual_wins += 1
                    manual_set_indices.append(i)
                elif winner == p2_name:
                    p2_manual_wins += 1
                    manual_set_indices.append(i)

        # Auto-fill logic for Best of 3
        if match_format == "Best of 3":
            if current_set_idx == 2:  # Set 3
                # If one player manually won 1 set, the other must have won the remaining set
                if p1_manual_wins == 1 and p2_manual_wins == 0:
                    # Find the empty set and fill with Player 2
                    for i in range(current_set_idx):
                        _, combo = self.prev_set_winners[i]
                        if combo.currentText() == "-":
                            combo.setCurrentText(p2_name)
                            self.auto_filled_sets.add(i)
                            break
                elif p2_manual_wins == 1 and p1_manual_wins == 0:
                    # Find the empty set and fill with Player 1
                    for i in range(current_set_idx):
                        _, combo = self.prev_set_winners[i]
                        if combo.currentText() == "-":
                            combo.setCurrentText(p1_name)
                            self.auto_filled_sets.add(i)
                            break

        # Auto-fill logic for Best of 5
        elif match_format == "Best of 5":
            if current_set_idx == 3:  # Set 4
                # If one player manually won 2 sets, the other must have won the remaining set
                if p1_manual_wins == 2 and p2_manual_wins == 0:
                    for i in range(current_set_idx):
                        _, combo = self.prev_set_winners[i]
                        if combo.currentText() == "-":
                            combo.setCurrentText(p2_name)
                            self.auto_filled_sets.add(i)
                            break
                elif p2_manual_wins == 2 and p1_manual_wins == 0:
                    for i in range(current_set_idx):
                        _, combo = self.prev_set_winners[i]
                        if combo.currentText() == "-":
                            combo.setCurrentText(p1_name)
                            self.auto_filled_sets.add(i)
                            break

            elif current_set_idx == 4:  # Set 5
                # If we're in set 5, each player must have won 2 sets
                if p1_manual_wins == 2 and p2_manual_wins == 0:
                    # Fill remaining 2 sets with Player 2
                    for i in range(current_set_idx):
                        _, combo = self.prev_set_winners[i]
                        if combo.currentText() == "-":
                            combo.setCurrentText(p2_name)
                            self.auto_filled_sets.add(i)
                elif p2_manual_wins == 2 and p1_manual_wins == 0:
                    # Fill remaining 2 sets with Player 1
                    for i in range(current_set_idx):
                        _, combo = self.prev_set_winners[i]
                        if combo.currentText() == "-":
                            combo.setCurrentText(p1_name)
                            self.auto_filled_sets.add(i)

    def update_previous_sets_visibility(self):
        """Update visibility of previous set winners based on current set number"""
        current_set_idx = self.set_number.currentIndex()

        # Show previous set inputs based on current set
        for i in range(4):
            label, combo = self.prev_set_winners[i]
            # Show if this is a previous set
            should_show = i < current_set_idx
            label.setVisible(should_show)
            combo.setVisible(should_show)

        # Clear auto-filled sets that are no longer visible
        self.auto_filled_sets = {i for i in self.auto_filled_sets if i < current_set_idx}

        # Trigger auto-fill logic when current set changes
        self.auto_fill_sets()

    def update_set_number_options(self):
        """Update available set numbers based on match format"""
        format_text = self.match_format.currentText()
        current_set = self.set_number.currentIndex()

        self.set_number.clear()
        if format_text == "Best of 3":
            self.set_number.addItems(["Set 1", "Set 2", "Set 3"])
        else:  # Best of 5
            self.set_number.addItems(["Set 1", "Set 2", "Set 3", "Set 4", "Set 5"])

        # Restore previous selection if valid
        if current_set < self.set_number.count():
            self.set_number.setCurrentIndex(current_set)

        # Update previous sets visibility
        self.update_previous_sets_visibility()

    def update_serving_combo(self):
        p1_code = self.player1_code.text().strip()
        p2_code = self.player2_code.text().strip()

        # Update starting server button labels
        if p1_code:
            self.player1_server_btn.setText(f"{p1_code}")
        else:
            self.player1_server_btn.setText("Player 1")

        if p2_code:
            self.player2_server_btn.setText(f"{p2_code}")
        else:
            self.player2_server_btn.setText("Player 2")

        # Update favorite button labels
        if p1_code:
            self.player1_favorite_btn.setText(f"{p1_code}")
        else:
            self.player1_favorite_btn.setText("Player 1")

        if p2_code:
            self.player2_favorite_btn.setText(f"{p2_code}")
        else:
            self.player2_favorite_btn.setText("Player 2")

        # Update previous set winner combos with full names
        for label, combo in self.prev_set_winners:
            current_selection = combo.currentText()
            combo.clear()
            combo.addItem("-")

            # Get full names
            p1_name = self.player1_name.text().strip()
            p2_name = self.player2_name.text().strip()

            # Use full names instead of just first names
            if p1_name:
                combo.addItem(p1_name)
            if p2_name:
                combo.addItem(p2_name)

            # Try to restore previous selection
            index = combo.findText(current_selection)
            if index >= 0:
                combo.setCurrentIndex(index)

    def analyze_match(self):
        # Get input data
        player1_name = self.player1_name.text().strip()
        player1_code = self.player1_code.text().strip()
        player2_name = self.player2_name.text().strip()
        player2_code = self.player2_code.text().strip()
        starting_server = self.get_starting_server()
        match_data = self.match_data.toPlainText()

        # Get match information
        match_format = "Bo5" if "5" in self.match_format.currentText() else "Bo3"
        set_number = self.set_number.currentIndex() + 1
        surface = self.surface_type.currentText() if self.surface_type.currentText() else None

        # Get favorite information using new method
        favorite, favorite_odds = self.get_favorite_info()

        # Get previous set winners (convert full names back to codes)
        previous_sets_winner = []
        for i in range(set_number - 1):
            _, combo = self.prev_set_winners[i]
            winner_text = combo.currentText()
            if winner_text != "-" and winner_text:
                # Convert full name to code
                winner_code = self.get_code_from_full_name(winner_text)
                if winner_code:
                    previous_sets_winner.append(winner_code)

        # Store match info for later use
        self.current_match_info = {
            'match_format': match_format,
            'set_number': set_number,
            'surface': surface,
            'favorite': favorite,
            'favorite_odds': favorite_odds,
            'previous_sets_winner': previous_sets_winner if previous_sets_winner else None,
            'tournament_level': self.tournament_level.currentText(),
            'tournament_name': self.tournament_name.text().strip()
        }

        # Validate input
        if not all([player1_name, player1_code, player2_name, player2_code, starting_server, match_data]):
            self.show_error("Please fill in all fields.")
            return

        try:
            # Reset outcome recorded flag when analyzing new data
            self.outcome_just_recorded = False
            # Reset AI prediction data when analyzing new data
            self.current_ai_prediction = None

            # Analyze the match
            self.predictor = EnhancedTennisPredictor(self.tracker)

            # Set match context for enhanced predictions
            if hasattr(self, 'current_match_info'):
                self.predictor.set_match_context(
                    set_number=self.current_match_info.get('set_number', 1),
                    match_format=self.current_match_info.get('match_format', 'Bo3'),
                    favorite=self.current_match_info.get('favorite'),
                    favorite_odds=self.current_match_info.get('favorite_odds'),
                    surface=self.current_match_info.get('surface'),
                    previous_sets_winner=self.current_match_info.get('previous_sets_winner')
                )

            game_analyses = self.predictor.parse_enhanced_game_data(
                match_data, player1_code, player2_code, starting_server
            )

            if not game_analyses:
                self.show_error("No games could be analyzed. Please check your data format.")
                return

            # Update all tabs
            self.update_analysis_tab(game_analyses)
            self.update_patterns_tab(player1_name, player1_code, player2_name, player2_code)
            self.update_predictions_tab(player1_name, player1_code, player2_name, player2_code)
            self.update_set_prediction_tab(player1_name, player1_code, player2_name, player2_code)



        except Exception as e:
            self.show_error(f"Analysis failed: {str(e)}")

    def analyze_match_silent(self):
        """Analyze match without showing error popups (for loading drafts)"""
        # Get input data
        player1_name = self.player1_name.text().strip()
        player1_code = self.player1_code.text().strip()
        player2_name = self.player2_name.text().strip()
        player2_code = self.player2_code.text().strip()
        starting_server = self.get_starting_server()
        match_data = self.match_data.toPlainText()

        # Get match information
        match_format = "Bo5" if "5" in self.match_format.currentText() else "Bo3"
        set_number = self.set_number.currentIndex() + 1
        surface = self.surface_type.currentText() if self.surface_type.currentText() else None

        # Get favorite information using new method
        favorite, favorite_odds = self.get_favorite_info()

        # Get previous set winners (convert first names back to codes)
        previous_sets_winner = []
        for i in range(set_number - 1):
            _, combo = self.prev_set_winners[i]
            winner_text = combo.currentText()
            if winner_text != "-" and winner_text:
                # Convert full name to code
                winner_code = self.get_code_from_full_name(winner_text)
                if winner_code:
                    previous_sets_winner.append(winner_code)

        # Store match info for later use
        self.current_match_info = {
            'match_format': match_format,
            'set_number': set_number,
            'surface': surface,
            'favorite': favorite,
            'favorite_odds': favorite_odds,
            'previous_sets_winner': previous_sets_winner if previous_sets_winner else None,
            'tournament_level': self.tournament_level.currentText(),
            'tournament_name': self.tournament_name.text().strip()
        }

        # Validate input - if missing, just return silently
        if not all([player1_name, player1_code, player2_name, player2_code, starting_server]):
            return

        # If no match data, just return silently (this is normal for drafts)
        if not match_data.strip():
            return

        try:
            # Reset outcome recorded flag when analyzing new data
            self.outcome_just_recorded = False
            # Reset AI prediction data when analyzing new data
            self.current_ai_prediction = None

            # Analyze the match
            self.predictor = EnhancedTennisPredictor(self.tracker)

            # Set match context for enhanced predictions
            if hasattr(self, 'current_match_info'):
                self.predictor.set_match_context(
                    set_number=self.current_match_info.get('set_number', 1),
                    match_format=self.current_match_info.get('match_format', 'Bo3'),
                    favorite=self.current_match_info.get('favorite'),
                    favorite_odds=self.current_match_info.get('favorite_odds'),
                    surface=self.current_match_info.get('surface'),
                    previous_sets_winner=self.current_match_info.get('previous_sets_winner')
                )

            game_analyses = self.predictor.parse_enhanced_game_data(
                match_data, player1_code, player2_code, starting_server
            )

            if not game_analyses:
                # No error popup for silent analysis - just return
                return

            # Update all tabs
            self.update_analysis_tab(game_analyses)
            self.update_patterns_tab(player1_name, player1_code, player2_name, player2_code)
            self.update_predictions_tab(player1_name, player1_code, player2_name, player2_code)
            self.update_set_prediction_tab(player1_name, player1_code, player2_name, player2_code)



        except Exception as e:
            # Silent failure - no error popup
            pass

    def update_analysis_tab(self, game_analyses):
        self.games_table.setRowCount(len(game_analyses))

        for i, game in enumerate(game_analyses):
            self.games_table.setItem(i, 0, QTableWidgetItem(str(game.game_number)))
            self.games_table.setItem(i, 1, QTableWidgetItem(game.server))
            self.games_table.setItem(i, 2, QTableWidgetItem(game.winner))
            self.games_table.setItem(i, 3, QTableWidgetItem("Hold" if game.server_won else "Break"))
            self.games_table.setItem(i, 4, QTableWidgetItem("✓" if game.first_point_won_by_server else "✗"))
            self.games_table.setItem(i, 5, QTableWidgetItem("✓" if game.three_point_run_by_server else "✗"))
            self.games_table.setItem(i, 6, QTableWidgetItem(f"{game.break_points_faced}/{game.break_points_saved}"))
            self.games_table.setItem(i, 7, QTableWidgetItem(game.outcome_type))
            self.games_table.setItem(i, 8, QTableWidgetItem(", ".join([mi.value for mi in game.momentum_indicators])))

            # Color code based on result
            if game.server_won:
                color = QColor(132, 99, 255)  # Light purple for hold
            else:
                color = QColor(255, 68, 68)  # Error color for break

            for j in range(9):
                self.games_table.item(i, j).setBackground(color)

    def update_patterns_tab(self, player1_name, player1_code, player2_name, player2_code):
        analysis = self.predictor.get_detailed_analysis()

        # Update player 1 patterns
        self.player1_patterns.setTitle(f"{player1_name} ({player1_code}) - Live Momentum")
        pattern1 = analysis['serve_patterns'].get(player1_code, {})
        self.update_pattern_labels(self.player1_pattern_labels, pattern1)

        # Update player 2 patterns
        self.player2_patterns.setTitle(f"{player2_name} ({player2_code}) - Live Momentum")
        pattern2 = analysis['serve_patterns'].get(player2_code, {})
        self.update_pattern_labels(self.player2_pattern_labels, pattern2)

    def update_pattern_labels(self, labels, pattern):
        if not pattern:
            # Clear all labels and progress bars
            for key, widget in labels.items():
                if hasattr(widget, 'setText'):
                    widget.setText("N/A")
                    widget.setStyleSheet("")
                elif hasattr(widget, 'setValue'):
                    widget.setValue(0)
            return

        # Enhanced pattern metrics with better formatting and progress bars
        try:
            # 1. CORE PERFORMANCE METRICS
            hold_percentage = pattern.get('games_held_percentage', 0.0)
            labels['hold_percentage'].setText(f"Games Held: {hold_percentage:.1%}")
            if 'hold_percentage_bar' in labels:
                labels['hold_percentage_bar'].setValue(int(hold_percentage * 100))

            # Color code hold percentage
            if hold_percentage >= 0.8:
                hold_style = f"color: {SUCCESS_COLOR}; font-weight: bold;"
            elif hold_percentage >= 0.6:
                hold_style = f"color: {SECONDARY_INTERACTIVE}; font-weight: bold;"
            elif hold_percentage < 0.4:
                hold_style = f"color: {ERROR_COLOR}; font-weight: bold;"
            else:
                hold_style = f"color: {PRIMARY_TEXT};"
            labels['hold_percentage'].setStyleSheet(hold_style)

            # Hold streak - get from serving_rhythm
            serving_rhythm = pattern.get('serving_rhythm', {})
            hold_streak = serving_rhythm.get('current_hold_streak', 0)
            labels['hold_streak'].setText(f"Current Hold Streak: {hold_streak} games")
            if hold_streak >= 5:
                labels['hold_streak'].setStyleSheet(f"color: {SUCCESS_COLOR}; font-weight: bold;")
            elif hold_streak >= 3:
                labels['hold_streak'].setStyleSheet(f"color: {SECONDARY_INTERACTIVE};")
            elif hold_streak == 0:
                labels['hold_streak'].setStyleSheet(f"color: {ERROR_COLOR}; font-weight: bold;")
            else:
                labels['hold_streak'].setStyleSheet(f"color: {PRIMARY_TEXT};")

            # Service consistency - get from serving_rhythm and display as x/10
            consistency_score = serving_rhythm.get('service_consistency_score', 0.0)
            labels['consistency_score'].setText(f"Service Consistency: {consistency_score:.1f}/10")
            if 'consistency_bar' in labels:
                # Normalize to 0-100 for progress bar (0-10 scale)
                consistency_percentage = min(100, int((consistency_score / 10.0) * 100))
                labels['consistency_bar'].setValue(consistency_percentage)

            # 2. MOMENTUM & FORM METRICS
            momentum_type = pattern.get('current_momentum', 'neutral')
            labels['momentum'].setText(f"Current Momentum: {momentum_type.replace('_', ' ').title()}")

            # Enhanced momentum metrics with safe defaults
            intensity = pattern.get('momentum_intensity_score', 5.0)
            duration = pattern.get('momentum_duration', 0)
            trend = pattern.get('momentum_trend', 'stable')

            labels['momentum_intensity'].setText(f"Intensity: {intensity:.1f}/10 ({trend})")
            labels['momentum_duration'].setText(f"Duration: {duration} games")

            # Recent form
            three_point_runs = pattern.get('recent_three_point_runs', 0)
            consecutive_015 = pattern.get('consecutive_015_starts', 0)
            labels['three_point_runs'].setText(f"3-Point Runs: {three_point_runs}")
            labels['consecutive_015'].setText(f"0-15 Starts: {consecutive_015}")

        except (KeyError, TypeError, ValueError) as e:
            print(f"⚠️  Error updating core pattern labels: {e}")
            return

        # 3. PRESSURE & MENTAL METRICS
        try:
            pressure_index = pattern.get('service_pressure_index', 0.0)
            clutch_rate = pattern.get('clutch_performance_rate', 0.0)
            fatigue = pattern.get('mental_fatigue_score', 0.0)
            break_points = pattern.get('break_points_in_set', 0)

            labels['pressure_index'].setText(f"Pressure Index: {pressure_index:.1f}/10")
            labels['mental_fatigue'].setText(f"Mental Fatigue: {fatigue:.1%}")
            labels['clutch_rate'].setText(f"Clutch Performance: {clutch_rate:.1%}")
            labels['break_points'].setText(f"Break Points: {break_points}")

        except (KeyError, TypeError, ValueError) as e:
            print(f"⚠️  Error updating pressure metrics: {e}")

        # 4. ADVANCED RHYTHM METRICS
        serving_rhythm = pattern.get('serving_rhythm', {})
        if serving_rhythm:
            try:
                # Recovery patterns
                recovery_0_15 = serving_rhythm.get('recovery_from_0_15', 0.0)
                recovery_0_30 = serving_rhythm.get('recovery_from_0_30', 0.0)
                recovery_0_40 = serving_rhythm.get('recovery_from_0_40', 0.0)

                labels['recovery_0_15'].setText(f"0-15: {recovery_0_15:.1%}")
                labels['recovery_0_30'].setText(f"0-30: {recovery_0_30:.1%}")
                labels['recovery_0_40'].setText(f"0-40: {recovery_0_40:.1%}")

                # Closing patterns
                closing_40_0 = serving_rhythm.get('closing_from_40_0', 0.0)
                closing_40_15 = serving_rhythm.get('closing_from_40_15', 0.0)
                closing_40_30 = serving_rhythm.get('closing_from_40_30', 0.0)

                labels['closing_40_0'].setText(f"40-0: {closing_40_0:.1%}")
                labels['closing_40_15'].setText(f"40-15: {closing_40_15:.1%}")
                labels['closing_40_30'].setText(f"40-30: {closing_40_30:.1%}")

                # Additional metrics
                deuce_win_rate = serving_rhythm.get('deuce_game_win_rate', 0.0)
                labels['deuce_win_rate'].setText(f"Deuce Win Rate: {deuce_win_rate:.1%}")

                # Service tempo - construct from quick/struggle holds data
                quick_holds = serving_rhythm.get('quick_holds', 0)
                struggle_holds = serving_rhythm.get('struggle_holds', 0)
                avg_points = serving_rhythm.get('avg_points_per_service_game', 0.0)

                if quick_holds > 0 or struggle_holds > 0 or avg_points > 0:
                    if quick_holds > struggle_holds:
                        tempo_text = f"Quick ({quick_holds}Q vs {struggle_holds}S, {avg_points:.1f}pts avg)"
                    elif struggle_holds > quick_holds:
                        tempo_text = f"Struggle ({quick_holds}Q vs {struggle_holds}S, {avg_points:.1f}pts avg)"
                    else:
                        tempo_text = f"Normal ({quick_holds}Q vs {struggle_holds}S, {avg_points:.1f}pts avg)"
                    labels['service_tempo'].setText(f"Service Tempo: {tempo_text}")
                else:
                    labels['service_tempo'].setText("Service Tempo: N/A")

            except (KeyError, TypeError, ValueError) as e:
                print(f"⚠️  Error updating rhythm metrics: {e}")
        else:
            # Set N/A for rhythm metrics if not available
            rhythm_keys = ['recovery_0_15', 'recovery_0_30', 'recovery_0_40',
                          'closing_40_0', 'closing_40_15', 'closing_40_30',
                          'deuce_win_rate', 'service_tempo']
            for key in rhythm_keys:
                if key in labels:
                    metric_name = key.replace('_', ' ').title()
                    labels[key].setText(f"{metric_name}: N/A")
                    labels[key].setStyleSheet("")

        # Apply enhanced color coding
        self.apply_enhanced_color_coding(labels, pattern)

    def apply_enhanced_color_coding(self, labels, pattern):
        """Apply enhanced color coding to pattern labels"""
        try:
            # Momentum color coding
            momentum_type = pattern.get('current_momentum', 'neutral')
            momentum_styles = {
                'strong_serving': f"color: {SUCCESS_COLOR}; font-weight: bold;",
                'weak_serving': f"color: {ERROR_COLOR}; font-weight: bold;",
                'break_point_pressure': f"color: {WARNING_COLOR}; font-weight: bold;",
                'momentum_shift': f"color: {ERROR_COLOR}; font-weight: bold;",
                'neutral': f"color: {PRIMARY_TEXT};"
            }
            momentum_style = momentum_styles.get(momentum_type, "color: rgb(0, 0, 0);")
            labels['momentum'].setStyleSheet(momentum_style)

            # Momentum intensity color coding
            intensity = pattern.get('momentum_intensity_score', 5.0)
            if intensity >= 7.5:
                intensity_style = f"color: {SUCCESS_COLOR}; font-weight: bold;"
            elif intensity >= 6.0:
                intensity_style = f"color: {SECONDARY_INTERACTIVE}; font-weight: bold;"
            elif intensity <= 2.5:
                intensity_style = f"color: {ERROR_COLOR}; font-weight: bold;"
            elif intensity <= 4.0:
                intensity_style = f"color: {WARNING_COLOR}; font-weight: bold;"
            else:
                intensity_style = f"color: {PRIMARY_TEXT};"
            labels['momentum_intensity'].setStyleSheet(intensity_style)

            # Pressure index color coding
            pressure_index = pattern.get('service_pressure_index', 0.0)
            if pressure_index >= 7.0:
                pressure_style = f"color: {ERROR_COLOR}; font-weight: bold;"
            elif pressure_index >= 4.0:
                pressure_style = f"color: {WARNING_COLOR}; font-weight: bold;"
            else:
                pressure_style = f"color: {PRIMARY_TEXT};"
            labels['pressure_index'].setStyleSheet(pressure_style)

            # Mental fatigue color coding
            fatigue = pattern.get('mental_fatigue_score', 0.0)
            if fatigue >= 0.7:
                fatigue_style = f"color: {ERROR_COLOR}; font-weight: bold;"
            elif fatigue >= 0.4:
                fatigue_style = f"color: {WARNING_COLOR}; font-weight: bold;"
            else:
                fatigue_style = f"color: {PRIMARY_TEXT};"
            labels['mental_fatigue'].setStyleSheet(fatigue_style)

            # Form indicators color coding
            three_point_runs = pattern.get('recent_three_point_runs', 0)
            if three_point_runs >= 3:
                labels['three_point_runs'].setStyleSheet(f"color: {SUCCESS_COLOR}; font-weight: bold;")
            elif three_point_runs >= 2:
                labels['three_point_runs'].setStyleSheet(f"color: {SECONDARY_INTERACTIVE};")
            else:
                labels['three_point_runs'].setStyleSheet(f"color: {PRIMARY_TEXT};")

            consecutive_015 = pattern.get('consecutive_015_starts', 0)
            if consecutive_015 >= 3:
                labels['consecutive_015'].setStyleSheet(f"color: {ERROR_COLOR}; font-weight: bold;")
            elif consecutive_015 >= 2:
                labels['consecutive_015'].setStyleSheet(f"color: {WARNING_COLOR}; font-weight: bold;")
            else:
                labels['consecutive_015'].setStyleSheet(f"color: {PRIMARY_TEXT};")

            # Service tempo color coding
            serving_rhythm = pattern.get('serving_rhythm', {})
            quick_holds = serving_rhythm.get('quick_holds', 0)
            struggle_holds = serving_rhythm.get('struggle_holds', 0)

            if quick_holds > struggle_holds and quick_holds > 0:
                labels['service_tempo'].setStyleSheet(f"color: {SUCCESS_COLOR};")  # Purple for good tempo
            elif struggle_holds > quick_holds and struggle_holds > 0:
                labels['service_tempo'].setStyleSheet(f"color: {ERROR_COLOR};")  # Red for poor tempo
            else:
                labels['service_tempo'].setStyleSheet(f"color: {PRIMARY_TEXT};")  # White for neutral

        except Exception as e:
            print(f"⚠️  Error applying enhanced color coding: {e}")



    def update_predictions_tab(self, player1_name, player1_code, player2_name, player2_code):
        # Determine next server
        if self.predictor.game_analyses:
            last_server = self.predictor.game_analyses[-1].server
            next_server = player2_code if last_server == player1_code else player1_code
        else:
            next_server = player1_code

        # Get current score and set number
        p1_games = sum(1 for g in self.predictor.game_analyses if g.winner == player1_code)
        p2_games = sum(1 for g in self.predictor.game_analyses if g.winner == player2_code)
        current_score = (p1_games, p2_games)
        set_number = self.set_number.currentIndex() + 1

        # Get prediction with EWMA weights
        prediction = self.predictor.predict_next_game_winner(next_server, player1_code, player2_code,
                                                           current_score, set_number)

        if prediction.get("prediction") == "insufficient_data":
            self.next_server_label.setText("Next Server: Insufficient Data")
            self.predicted_winner_label.setText("Predicted Winner: Insufficient Data")
            self.hold_prob_bar.setValue(0)
            self.break_prob_bar.setValue(0)
            self.confidence_label.setText("Confidence: N/A")
            return

        # Update prediction display
        next_server_name = player1_name if next_server == player1_code else player2_name
        predicted_winner_name = (player1_name if prediction["predicted_winner"] == player1_code
                               else player2_name)

        self.next_server_label.setText(f"Next Server: {next_server_name} ({next_server})")
        self.predicted_winner_label.setText(f"Predicted Winner: {predicted_winner_name} ({prediction['predicted_winner']})")

        # Update probability bars
        self.hold_prob_bar.setValue(int(prediction['hold_probability'] * 100))
        self.break_prob_bar.setValue(int(prediction['break_probability'] * 100))

        # Update confidence with EWMA adjustment info
        confidence_text = f"Confidence: {prediction['confidence']:.1%}"
        if 'confidence_multiplier' in prediction:
            confidence_text += f" (×{prediction['confidence_multiplier']:.2f})"
        self.confidence_label.setText(confidence_text)

        # Update momentum factors with enhanced metrics and EWMA info
        momentum = prediction['momentum_indicators']

        # Get enhanced metrics for the next server
        analysis = self.predictor.get_detailed_analysis()
        server_pattern = analysis['serve_patterns'].get(next_server, {})

        momentum_text = f"""
=== Traditional Momentum ===
Server Momentum: {momentum['server_momentum']}
Consecutive 0-15 Starts: {momentum['consecutive_015_starts']}
Recent 3-Point Runs: {momentum['recent_three_point_runs']}
Break Points (Entire Set): {momentum['break_points_in_set']}
Overall Hold Rate: {momentum['games_held_percentage']:.1%}

=== Enhanced Psychological Analysis ===
Momentum Intensity: {server_pattern.get('momentum_intensity_score', 5.0):.1f}/10
Momentum Duration: {server_pattern.get('momentum_duration', 0)} games
Trend Direction: {server_pattern.get('momentum_trend', 'stable')}
Service Pressure Index: {server_pattern.get('service_pressure_index', 0.0):.1f}/10
Clutch Performance Rate: {server_pattern.get('clutch_performance_rate', 0.0):.1%}
Mental Fatigue Score: {server_pattern.get('mental_fatigue_score', 0.0):.1%}
Pressure Response: {server_pattern.get('pressure_response_pattern', 'unknown')}
"""

        # Add EWMA adjustments if available
        if 'ewma_adjustments' in prediction:
            ewma = prediction['ewma_adjustments']
            momentum_text += f"\n--- Dynamic Weights (EWMA) ---\n"
            momentum_text += f"Total Adjustment: {ewma['total_adjustment']:+.1%}\n"
            momentum_text += f"Score Category: {ewma['score_category']}\n"
            if ewma.get('recent_accuracy') is not None:
                momentum_text += f"Recent Prediction Accuracy: {ewma['recent_accuracy']:.1%}\n"

        self.momentum_text.setPlainText(momentum_text)

        # Update insights
        insights = self.generate_insights(player1_code, player2_code, player1_name, player2_name)
        self.insights_text.setPlainText("\n".join([f"• {insight}" for insight in insights]))

    def generate_insights(self, player1_code, player2_code, player1_name, player2_name):
        insights = []
        analysis = self.predictor.get_detailed_analysis()

        for player_code, player_name in [(player1_code, player1_name), (player2_code, player2_name)]:
            pattern = analysis['serve_patterns'].get(player_code)
            if not pattern:
                continue

            # Enhanced insight generation with contextual prioritization
            player_insights = []

            # Calculate insight priority scores to avoid contradictions
            weakness_score = 0
            strength_score = 0
            pressure_score = 0

            # Weakness indicators
            if pattern['consecutive_015_starts'] >= 3:
                weakness_score += 3
                player_insights.append(('weakness', f"{player_name} has {pattern['consecutive_015_starts']} consecutive 0-15 starts - severe serving weakness"))
            elif pattern['consecutive_015_starts'] >= 2:
                weakness_score += 2
                player_insights.append(('weakness', f"{player_name} has {pattern['consecutive_015_starts']} consecutive 0-15 starts - serving weakness"))

            # Strength indicators
            if pattern['recent_three_point_runs'] >= 3:
                strength_score += 3
                player_insights.append(('strength', f"{player_name} has {pattern['recent_three_point_runs']} recent 3-point runs - dominant serving"))
            elif pattern['recent_three_point_runs'] >= 2:
                strength_score += 2
                player_insights.append(('strength', f"{player_name} has {pattern['recent_three_point_runs']} recent 3-point runs - serving strength"))

            # Pressure indicators with context (adjusted for set-wide counting)
            bp_count = pattern['break_points_in_set']
            if bp_count >= 8:  # Adjusted threshold for set-wide counting
                pressure_score += 3
                player_insights.append(('pressure', f"{player_name} faced {bp_count} break points in set - extreme pressure"))
            elif bp_count >= 5:  # Adjusted threshold for set-wide counting
                pressure_score += 2
                player_insights.append(('pressure', f"{player_name} faced {bp_count} break points in set - under pressure"))

            # Momentum insights with enhanced context
            momentum = pattern.get('current_momentum', 'neutral')
            if momentum == 'momentum_shift':
                player_insights.append(('momentum', f"{player_name} showing momentum shift - vulnerable to breaks"))
            elif momentum == 'strong_serving' and strength_score >= weakness_score:
                player_insights.append(('momentum', f"{player_name} showing strong serving momentum"))
            elif momentum == 'break_point_pressure':
                player_insights.append(('momentum', f"{player_name} under break point pressure"))

            # Enhanced psychological insights
            intensity = pattern.get('momentum_intensity_score', 5.0)
            duration = pattern.get('momentum_duration', 0)
            clutch_rate = pattern.get('clutch_performance_rate', 0.0)
            fatigue = pattern.get('mental_fatigue_score', 0.0)
            pressure_response = pattern.get('pressure_response_pattern', 'unknown')

            # Momentum intensity insights
            if intensity >= 8.0:
                player_insights.append(('psychological', f"{player_name} has exceptional momentum (intensity {intensity:.1f}/10) - very confident"))
            elif intensity <= 2.5:
                player_insights.append(('psychological', f"{player_name} has very poor momentum (intensity {intensity:.1f}/10) - struggling mentally"))
            elif intensity >= 7.0:
                player_insights.append(('psychological', f"{player_name} has strong momentum (intensity {intensity:.1f}/10)"))
            elif intensity <= 3.5:
                player_insights.append(('psychological', f"{player_name} has weak momentum (intensity {intensity:.1f}/10)"))

            # Duration insights
            if duration >= 4:
                player_insights.append(('psychological', f"{player_name}'s current momentum has lasted {duration} games - well established"))
            elif duration >= 2:
                player_insights.append(('psychological', f"{player_name}'s momentum building over {duration} games"))

            # Clutch performance insights
            if clutch_rate >= 0.8:
                player_insights.append(('psychological', f"{player_name} excellent under pressure ({clutch_rate:.0%} clutch rate) - mentally tough"))
            elif clutch_rate <= 0.3 and pattern.get('pressure_situations_faced', 0) >= 3:
                player_insights.append(('psychological', f"{player_name} struggles under pressure ({clutch_rate:.0%} clutch rate) - may choke"))

            # Mental fatigue insights
            if fatigue >= 0.7:
                player_insights.append(('psychological', f"{player_name} showing high mental fatigue ({fatigue:.0%}) - may decline"))
            elif fatigue >= 0.5:
                player_insights.append(('psychological', f"{player_name} showing moderate mental fatigue ({fatigue:.0%})"))

            # Pressure response pattern insights
            if pressure_response == 'clutch':
                player_insights.append(('psychological', f"{player_name} has clutch response pattern - performs better under pressure"))
            elif pressure_response == 'choking':
                player_insights.append(('psychological', f"{player_name} has choking response pattern - vulnerable under pressure"))

            # Filter insights to prevent contradictions
            # Priority: pressure > weakness > strength > momentum > psychological
            final_insights = []

            # Add pressure insights (highest priority)
            pressure_insights = [insight for category, insight in player_insights if category == 'pressure']
            if pressure_insights:
                final_insights.extend(pressure_insights)

            # Add weakness or strength (mutually exclusive)
            if weakness_score > strength_score:
                weakness_insights = [insight for category, insight in player_insights if category == 'weakness']
                final_insights.extend(weakness_insights)
            elif strength_score > weakness_score:
                strength_insights = [insight for category, insight in player_insights if category == 'strength']
                final_insights.extend(strength_insights)

            # Add momentum insights if they don't contradict
            momentum_insights = [insight for category, insight in player_insights if category == 'momentum']
            for momentum_insight in momentum_insights:
                # Only add momentum insights that don't contradict the main narrative
                if ('vulnerable' in momentum_insight and weakness_score >= strength_score) or \
                   ('strong' in momentum_insight and strength_score > weakness_score) or \
                   ('pressure' in momentum_insight):
                    final_insights.append(momentum_insight)

            # Add psychological insights (complementary, not contradictory)
            psychological_insights = [insight for category, insight in player_insights if category == 'psychological']
            # Limit to most important psychological insights (max 2 per player)
            final_insights.extend(psychological_insights[:2])

            insights.extend(final_insights)

            # Remove duplicate momentum insights - they're already handled above in final_insights

        return insights

    def update_set_prediction_tab(self, player1_name, player1_code, player2_name, player2_code):
        """
        Update the set prediction tab with current analysis.
        """
        # Get set prediction with current set number
        set_number = self.set_number.currentIndex() + 1
        set_prediction = self.predictor.predict_current_set_winner(player1_code, player2_code,
                                                                 set_number=set_number)

        # Update current set display
        self.current_set_label.setText(f"Set {set_number}")

        if set_prediction.get("prediction") == "insufficient_games":
            # Not enough games played
            self.set_requirements_label.setText(set_prediction["message"])
            self.set_requirements_label.setStyleSheet(f"color: {WARNING_COLOR}; font-weight: bold;")

            self.current_set_score_label.setText("Score: Insufficient Data")
            self.games_played_label.setText(f"Games played: {set_prediction['games_played']}")

            # Clear all prediction displays
            self.player1_set_prob_label.setText(f"{player1_name}: N/A")
            self.player2_set_prob_label.setText(f"{player2_name}: N/A")
            self.player1_set_prob_bar.setValue(0)
            self.player2_set_prob_bar.setValue(0)
            self.set_confidence_label.setText("Confidence: N/A")
            self.player1_hold_prob_label.setText(f"{player1_name} Hold Rate: N/A")
            self.player2_hold_prob_label.setText(f"{player2_name} Hold Rate: N/A")
            self.set_scenarios_text.setPlainText("Need at least 6 games for set prediction.")

            # Disable outcome recording
            self.player1_win_button.setEnabled(False)
            self.player2_win_button.setEnabled(False)
            self.current_pred_info_label.setText("No prediction available - need at least 6 games")

        elif set_prediction.get("set_complete"):
            # Set is already complete
            winner_name = player1_name if set_prediction["winner"] == player1_code else player2_name
            final_score = set_prediction["final_score"]

            self.set_requirements_label.setText("Set is complete!")
            self.set_requirements_label.setStyleSheet(f"color: {SUCCESS_COLOR}; font-weight: bold;")

            self.current_set_score_label.setText(f"Final Score: {final_score[0]}-{final_score[1]}")
            self.games_played_label.setText(f"Games played: {sum(final_score)}")

            self.player1_set_prob_label.setText(f"{player1_name}: {set_prediction['player1_probability']:.0%}")
            self.player2_set_prob_label.setText(f"{player2_name}: {set_prediction['player2_probability']:.0%}")
            self.player1_set_prob_bar.setValue(int(set_prediction['player1_probability'] * 100))
            self.player2_set_prob_bar.setValue(int(set_prediction['player2_probability'] * 100))

            self.set_confidence_label.setText("Confidence: 100% (Set Complete)")
            self.set_scenarios_text.setPlainText(f"Set complete! Winner: {winner_name}")

            # Disable outcome recording for completed sets
            self.player1_win_button.setEnabled(False)
            self.player2_win_button.setEnabled(False)
            self.current_pred_info_label.setText("Set already complete - no prediction to record")

        else:
            # Active set prediction
            self.set_requirements_label.setText("Set prediction available!")
            self.set_requirements_label.setStyleSheet(f"color: {SUCCESS_COLOR}; font-weight: bold;")

            current_score = set_prediction["current_score"]
            self.current_set_score_label.setText(f"Current Score: {current_score[0]}-{current_score[1]}")
            self.games_played_label.setText(f"Games played: {set_prediction['games_played']}")

            # Check if we just recorded an outcome for this specific score
            if self.outcome_just_recorded and self.last_recorded_score == current_score:
                self.player1_win_button.setEnabled(False)
                self.player2_win_button.setEnabled(False)
                self.current_pred_info_label.setText("Outcome already recorded for this score")
                # Don't create a new prediction, just update display
                return

            # Check if we're in auto-analysis mode (loading match) - don't create NEW predictions
            # but allow outcome recording for existing AI predictions only
            if self.auto_analysis_mode:
                # Check if we have existing AI predictions for this score that need outcome recording
                existing_ai_predictions = [p for p in self.tracker.predictions
                                         if (p.actual_winner is None and
                                             p.score == current_score and
                                             p.player1_code == player1_code and
                                             p.player2_code == player2_code and
                                             p.is_ai_prediction)]

                if existing_ai_predictions:
                    # We have existing AI predictions - allow outcome recording
                    self.current_prediction = existing_ai_predictions[0]
                    self.player1_win_button.setEnabled(True)
                    self.player2_win_button.setEnabled(True)
                    self.player1_win_button.setText(f"{player1_name} Won")
                    self.player2_win_button.setText(f"{player2_name} Won")

                    # Update current prediction info
                    self.current_pred_info_label.setText(
                        f"AI Prediction active - Score: {self.current_prediction.score[0]}-{self.current_prediction.score[1]}, "
                        f"Predicted: {self.current_prediction.predicted_winner} ({self.current_prediction.prediction_probability * 100:.1f}%)"
                    )
                else:
                    # No existing AI predictions - disable buttons and don't create new ones
                    self.player1_win_button.setEnabled(False)
                    self.player2_win_button.setEnabled(False)
                    self.current_pred_info_label.setText("Match loaded - click 'Get AI Analysis' to enable outcome recording")

                # Don't create new predictions during automatic analysis, but continue with display updates
                # Remove the return statement to allow probability and display updates to continue

            # Update probabilities
            p1_prob = set_prediction["player1_probability"]
            p2_prob = set_prediction["player2_probability"]

            # Add indicator for 50/50 cases
            p1_text = f"{player1_name}: {p1_prob:.1%}"
            p2_text = f"{player2_name}: {p2_prob:.1%}"

            if p1_prob == p2_prob:
                match_info = getattr(self, 'current_match_info', {})
                favorite = match_info.get('favorite')
                if favorite == player1_code:
                    p1_text += " [Favorite]"
                elif favorite == player2_code:
                    p2_text += " [Favorite]"
                else:
                    p1_text += " [Default]"

            self.player1_set_prob_label.setText(p1_text)
            self.player2_set_prob_label.setText(p2_text)
            self.player1_set_prob_bar.setValue(int(p1_prob * 100))
            self.player2_set_prob_bar.setValue(int(p2_prob * 100))

            # Color code based on actual predicted winner (must match the logic below)
            # This ensures highlighting matches the recorded prediction
            if p1_prob == p2_prob:
                # Handle 50/50 case - highlight the one that will be predicted
                match_info = getattr(self, 'current_match_info', {})
                favorite = match_info.get('favorite')
                highlight_p1 = favorite == player1_code if favorite in [player1_code, player2_code] else True

                if highlight_p1:
                    self.player1_set_prob_label.setStyleSheet(f"color: {SUCCESS_COLOR}; font-weight: bold;")
                    self.player2_set_prob_label.setStyleSheet(f"color: {PRIMARY_TEXT}; font-weight: normal;")
                else:
                    self.player1_set_prob_label.setStyleSheet(f"color: {PRIMARY_TEXT}; font-weight: normal;")
                    self.player2_set_prob_label.setStyleSheet(f"color: {SUCCESS_COLOR}; font-weight: bold;")
            elif p1_prob > p2_prob:
                self.player1_set_prob_label.setStyleSheet(f"color: {SUCCESS_COLOR}; font-weight: bold;")
                self.player2_set_prob_label.setStyleSheet(f"color: {PRIMARY_TEXT}; font-weight: normal;")
            else:
                self.player1_set_prob_label.setStyleSheet(f"color: {PRIMARY_TEXT}; font-weight: normal;")
                self.player2_set_prob_label.setStyleSheet(f"color: {SUCCESS_COLOR}; font-weight: bold;")

            # Extract games from current_score for momentum adjustment check
            p1_games, p2_games = current_score

            # Show momentum adjustment info if significant
            if abs(p1_games - p2_games) <= 1 and set_prediction.get("momentum_factors"):
                momentum_info = "\nMomentum-adjusted prediction"
                # Set the text rather than append
                scenario_text = self.set_scenarios_text.toPlainText()
                self.set_scenarios_text.setPlainText(scenario_text + momentum_info)

            # Update confidence
            confidence = set_prediction["confidence"]
            self.set_confidence_label.setText(f"Confidence: {confidence:.1%}")

            # Update hold probabilities with EWMA info
            p1_hold = set_prediction["player1_hold_probability"]
            p2_hold = set_prediction["player2_hold_probability"]

            # Get base hold rates if available
            p1_pattern = self.predictor.serve_patterns.get(player1_code)
            p2_pattern = self.predictor.serve_patterns.get(player2_code)

            p1_text = f"{player1_name} Hold Rate: {p1_hold:.1%}"
            p2_text = f"{player2_name} Hold Rate: {p2_hold:.1%}"

            if p1_pattern and p1_pattern.games_held_percentage > 0:
                base_p1 = p1_pattern.games_held_percentage
                adj_p1 = p1_hold - base_p1
                p1_text += f" (Base: {base_p1:.1%}, Adj: {adj_p1:+.1%})"

            if p2_pattern and p2_pattern.games_held_percentage > 0:
                base_p2 = p2_pattern.games_held_percentage
                adj_p2 = p2_hold - base_p2
                p2_text += f" (Base: {base_p2:.1%}, Adj: {adj_p2:+.1%})"

            self.player1_hold_prob_label.setText(p1_text)
            self.player2_hold_prob_label.setText(p2_text)

            # Start with empty scenarios text - betting recommendations contain all needed info
            scenarios_text = ""

            # Add betting recommendations if betting system is available
            if hasattr(self, 'betting_system') and self.betting_system is not None:
                try:
                    # Update betting system with latest data
                    self.update_betting_system_data()

                    # Extract predicted winner from set prediction probabilities
                    p1_prob = set_prediction.get('player1_probability', 0.5)
                    p2_prob = set_prediction.get('player2_probability', 0.5)
                    set_predicted_winner = player1_code if p1_prob > p2_prob else player2_code

                    scenarios_text += self.generate_betting_recommendations(
                        current_score, set_number, set_predicted_winner,
                        set_prediction.get('confidence', 0.5), set_prediction
                    )
                except Exception as e:
                    scenarios_text += f"\n\n--- 💰 BETTING RECOMMENDATION ---\n• Error: {str(e)}"

            self.set_scenarios_text.setPlainText(scenarios_text)

            # Check if we have an AI prediction available for this score
            # Only process AI predictions if we're not in auto-analysis mode or if we have existing predictions
            if (self.current_ai_prediction and
                self.current_ai_prediction['score'] == current_score and
                not self.auto_analysis_mode):

                # Check if we already have a pending AI prediction for this exact score in the tracker
                existing_ai_predictions = [p for p in self.tracker.predictions
                                          if (p.actual_winner is None and
                                              p.score == current_score and
                                              p.player1_code == player1_code and
                                              p.player2_code == player2_code and
                                              p.is_ai_prediction)]

                if existing_ai_predictions:
                    # Update existing AI prediction with latest analysis results
                    existing_prediction = existing_ai_predictions[0]  # Use the first (should be only one)
                    ai_pred = self.current_ai_prediction

                    # Update the existing prediction with latest AI analysis data
                    existing_prediction.predicted_winner = ai_pred['predicted_winner']
                    existing_prediction.prediction_probability = ai_pred['prediction_probability']
                    existing_prediction.ai_analysis_text = ai_pred['ai_analysis_text']
                    existing_prediction.ai_model_used = ai_pred['ai_model_used']
                    existing_prediction.timestamp = datetime.now().isoformat()  # Update timestamp to latest analysis

                    # Save the updated prediction
                    self.tracker.save_data()

                    self.current_prediction = existing_prediction
                    self.player1_win_button.setEnabled(True)
                    self.player2_win_button.setEnabled(True)
                    self.player1_win_button.setText(f"{player1_name} Won")
                    self.player2_win_button.setText(f"{player2_name} Won")

                    # Update current prediction info with latest data
                    prediction_type = "AI" if self.current_prediction.is_ai_prediction else "Mathematical"
                    self.current_pred_info_label.setText(
                        f"{prediction_type} Prediction active - Score: {self.current_prediction.score[0]}-{self.current_prediction.score[1]}, "
                        f"Predicted: {self.current_prediction.predicted_winner} ({self.current_prediction.prediction_probability * 100:.1f}%)"
                    )

                    print(f"🔄 Updated existing AI prediction for score {current_score} with latest analysis results")
                else:
                    # Check if there's an existing mathematical prediction for this score
                    existing_math_predictions = [p for p in self.tracker.predictions
                                               if (p.actual_winner is None and
                                                   p.score == current_score and
                                                   p.player1_code == player1_code and
                                                   p.player2_code == player2_code and
                                                   not p.is_ai_prediction)]

                    # Keep existing mathematical prediction - we want to compare both systems
                    if existing_math_predictions:
                        print(f"📊 Mathematical prediction exists for score {current_score} - will compare both AI and Math systems")

                    # Create new AI prediction for tracking
                    ai_pred = self.current_ai_prediction
                    match_info = getattr(self, 'current_match_info', {})

                    # Extract adaptive learning data from AI prediction
                    adaptive_weights = ai_pred.get('adaptive_weights', {})
                    prediction_context = ai_pred.get('prediction_context', {})

                    self.current_prediction = self.tracker.add_prediction(
                        score=current_score,
                        player1_name=ai_pred['player1_name'],
                        player2_name=ai_pred['player2_name'],
                        player1_code=ai_pred['player1_code'],
                        player2_code=ai_pred['player2_code'],
                        predicted_winner=ai_pred['predicted_winner'],
                        prediction_probability=ai_pred['prediction_probability'],
                        confidence=1.0,  # AI predictions have full confidence
                        momentum_factors=set_prediction.get("momentum_factors", {}),
                        set_number=ai_pred['set_number'],
                        match_format=match_info.get('match_format', 'Bo3'),
                        favorite=match_info.get('favorite'),
                        favorite_odds=match_info.get('favorite_odds'),
                        surface=match_info.get('surface'),
                        previous_sets_winner=match_info.get('previous_sets_winner'),
                        is_ai_prediction=True,
                        ai_analysis_text=ai_pred['ai_analysis_text'],
                        ai_model_used=ai_pred['ai_model_used'],
                        # Adaptive learning fields
                        prompt_weights=adaptive_weights,
                        context_factors=prediction_context,
                        learning_metadata={
                            'timestamp': datetime.now().isoformat(),
                            'gui_version': 'enhanced_gui_v4',
                            'tournament_level': self.tournament_level.currentText(),
                            'tournament_name': self.tournament_name.text().strip()
                        },
                        prompt_version=adaptive_weights.get('version', '1.0'),
                        weight_source='adaptive_learning',
                        session_id=getattr(self, 'current_session_id', None) or "default_session",
                        match_status="pending",  # New predictions start as pending
                        prediction_id=ai_pred.get('prediction_id')  # Link to enhanced learning system
                    )

                    # Enable Win/Loss buttons and update display
                    self.player1_win_button.setEnabled(True)
                    self.player2_win_button.setEnabled(True)
                    self.player1_win_button.setText(f"{player1_name} Won")
                    self.player2_win_button.setText(f"{player2_name} Won")

                    # Update current prediction info
                    self.current_pred_info_label.setText(
                        f"AI Prediction saved - Score: {current_score[0]}-{current_score[1]}, "
                        f"Predicted: {ai_pred['predicted_winner']} ({ai_pred['prediction_probability'] * 100:.1f}%)"
                    )
            else:
                # No AI prediction available - check for existing AI predictions only
                # Only enable outcome recording buttons for AI predictions
                existing_ai_predictions = [p for p in self.tracker.predictions
                                         if (p.actual_winner is None and
                                             p.score == current_score and
                                             p.player1_code == player1_code and
                                             p.player2_code == player2_code and
                                             p.is_ai_prediction)]

                if existing_ai_predictions:
                    # Already have a pending AI prediction for this score, enable outcome recording
                    self.current_prediction = existing_ai_predictions[0]  # Use the first (should be only one)
                    self.player1_win_button.setEnabled(True)
                    self.player2_win_button.setEnabled(True)
                    self.player1_win_button.setText(f"{player1_name} Won")
                    self.player2_win_button.setText(f"{player2_name} Won")

                    # Update current prediction info
                    self.current_pred_info_label.setText(
                        f"AI Prediction active - Score: {self.current_prediction.score[0]}-{self.current_prediction.score[1]}, "
                        f"Predicted: {self.current_prediction.predicted_winner} ({self.current_prediction.prediction_probability * 100:.1f}%)"
                    )
                else:
                    # No AI predictions available - disable outcome recording buttons
                    self.player1_win_button.setEnabled(False)
                    self.player2_win_button.setEnabled(False)
                    self.current_pred_info_label.setText("Click 'Get AI Analysis' to enable outcome recording")

                    # Still create mathematical prediction for display purposes (if not in auto-analysis mode)
                    if not self.auto_analysis_mode:
                        # Create new mathematical prediction based on set prediction algorithm
                        # Determine predicted winner based on set probabilities
                        p1_prob = set_prediction['player1_probability']
                        p2_prob = set_prediction['player2_probability']
                        predicted_winner = player1_code if p1_prob > p2_prob else player2_code
                        prediction_probability = max(p1_prob, p2_prob)

                        # Get match info for enhanced prediction
                        match_info = getattr(self, 'current_match_info', {})

                        # Create mathematical prediction (for tracking purposes only, not for outcome recording)
                        mathematical_prediction = self.tracker.add_prediction(
                            score=current_score,
                            player1_name=player1_name,
                            player2_name=player2_name,
                            player1_code=player1_code,
                            player2_code=player2_code,
                            predicted_winner=predicted_winner,
                            prediction_probability=prediction_probability,
                            confidence=set_prediction.get('confidence', 0.5),
                            momentum_factors=set_prediction.get("momentum_factors", {}),
                            set_number=set_number,
                            match_format=match_info.get('match_format', 'Bo3'),
                            favorite=match_info.get('favorite'),
                            favorite_odds=match_info.get('favorite_odds'),
                            surface=match_info.get('surface'),
                            previous_sets_winner=match_info.get('previous_sets_winner'),
                            is_ai_prediction=False,  # This is a mathematical prediction
                            learning_metadata={
                                'timestamp': datetime.now().isoformat(),
                                'gui_version': 'enhanced_gui_v4',
                                'tournament_level': self.tournament_level.currentText(),
                                'tournament_name': self.tournament_name.text().strip()
                            }
                        )

                        # Do NOT enable Win/Loss buttons for mathematical predictions
                        # Buttons remain disabled - only AI predictions enable outcome recording
                        self.player1_win_button.setText(f"{player1_name} Won")
                        self.player2_win_button.setText(f"{player2_name} Won")

        # Update AI analysis status
        if hasattr(self, 'ai_status_label'):
            self.check_gemini_api_status()

    def clear_all(self):
        self.player1_name.clear()
        self.player1_code.clear()
        self.player2_name.clear()
        self.player2_code.clear()
        self.match_data.clear()

        # Reset starting server selection
        self.set_starting_server(1)  # Set to "Player 1" (default)

        # Reset favorite selection
        self.set_favorite(0)  # Set to "No Favorite"

        # Reset current set to Set 1
        self.set_number.setCurrentIndex(0)  # Set to "Set 1"

        # Only reset tournament info if preserve checkbox is NOT checked
        if not self.preserve_tournament_checkbox.isChecked():
            # Reset tournament level to ATP (default)
            self.tournament_level.setCurrentText("ATP")
            # Clear tournament name
            self.tournament_name.clear()

        # Reset previous set winners
        for label, combo in self.prev_set_winners:
            combo.setCurrentIndex(0)  # Set to "-"

        # Clear auto-filled tracking
        self.auto_filled_sets.clear()

        # Save to Queue button is always enabled now (draft mode)

        # Update current session display
        self.update_current_session_display()

        # Clear tables and displays
        self.games_table.setRowCount(0)
        self.next_server_label.setText("Next Server: N/A")
        self.predicted_winner_label.setText("Predicted Winner: N/A")
        self.hold_prob_bar.setValue(0)
        self.break_prob_bar.setValue(0)
        self.confidence_label.setText("Confidence: N/A")
        self.momentum_text.clear()
        self.insights_text.clear()

        # Clear set prediction tab
        self.set_requirements_label.setText("At least 6 games must be played for set prediction")
        self.set_requirements_label.setStyleSheet(f"color: {PRIMARY_TEXT}; font-weight: normal;")
        self.current_set_score_label.setText("Score: N/A")
        self.games_played_label.setText("Games played: N/A")
        self.player1_set_prob_label.setText("Player 1: N/A")
        self.player2_set_prob_label.setText("Player 2: N/A")
        self.player1_set_prob_bar.setValue(0)
        self.player2_set_prob_bar.setValue(0)
        self.set_confidence_label.setText("Confidence: N/A")
        self.player1_hold_prob_label.setText("Player 1 Hold Rate: N/A")
        self.player2_hold_prob_label.setText("Player 2 Hold Rate: N/A")
        self.set_scenarios_text.clear()

        # Clear prediction tracking UI
        self.current_pred_info_label.setText("No prediction to record")
        self.player1_win_button.setEnabled(False)
        self.player2_win_button.setEnabled(False)
        self.player1_win_button.setText("Player 1 Won")
        self.player2_win_button.setText("Player 2 Won")
        self.current_prediction = None

    def show_error(self, message):
        QMessageBox.critical(self, "Error", message)

    def show_auto_close_notification(self, title: str, message: str, duration_ms: int = 1000):
        """Show a notification that automatically closes after specified duration"""
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setIcon(QMessageBox.Information)
        msg_box.setStandardButtons(QMessageBox.NoButton)  # No buttons

        # Create timer to auto-close
        timer = QTimer()
        timer.timeout.connect(msg_box.accept)
        timer.setSingleShot(True)
        timer.start(duration_ms)

        # Show the message box
        msg_box.exec_()

    def export_analysis(self):
        """Export game analysis to a text file for debugging."""
        if not hasattr(self, 'predictor') or not self.predictor.game_analyses:
            self.show_error("No analysis to export. Please analyze a match first.")
            return

        # Get filename from user
        filename, _ = QFileDialog.getSaveFileName(
            self, "Export Analysis", "", "Text Files (*.txt);;All Files (*)"
        )

        if not filename:
            return

        try:
            with open(filename, 'w') as f:
                # Write header
                f.write("Tennis Match Analysis Export\n")
                f.write("=" * 80 + "\n\n")

                # Write player information
                f.write(f"Player 1: {self.player1_name.text()} ({self.player1_code.text()})\n")
                f.write(f"Player 2: {self.player2_name.text()} ({self.player2_code.text()})\n")
                f.write(f"Starting Server: {self.starting_server.currentText()}\n\n")

                # Write detailed game-by-game analysis
                f.write("Game-by-Game Analysis:\n")
                f.write("-" * 80 + "\n\n")

                for game in self.predictor.game_analyses:
                    f.write(f"Game {game.game_number}:\n")
                    f.write(f"  Server: {game.server}\n")
                    f.write(f"  Winner: {game.winner}\n")
                    f.write(f"  Server won: {game.server_won} ({'HOLD' if game.server_won else 'BREAK'})\n")
                    f.write(f"  Outcome type: {game.outcome_type}\n")
                    f.write(f"  First point won by server: {game.first_point_won_by_server}\n")
                    f.write(f"  Three-point run by server: {game.three_point_run_by_server}\n")
                    f.write(f"  Break points faced: {game.break_points_faced}\n")
                    f.write(f"  Break points saved: {game.break_points_saved}\n")
                    f.write(f"  Momentum indicators: {[mi.value for mi in game.momentum_indicators]}\n")
                    f.write(f"  Point-by-point progression:\n")
                    for i, (p1, p2) in enumerate(game.point_by_point):
                        f.write(f"    Point {i+1}: {p1}-{p2}\n")
                    f.write("\n")

                # Write serving patterns
                f.write("\nServing Patterns:\n")
                f.write("-" * 80 + "\n\n")

                analysis = self.predictor.get_detailed_analysis()
                for player, pattern in analysis['serve_patterns'].items():
                    f.write(f"{player}:\n")
                    f.write(f"  Games held percentage: {pattern['games_held_percentage']:.1%}\n")
                    f.write(f"  Consecutive 0-15 starts: {pattern['consecutive_015_starts']}\n")
                    f.write(f"  Recent three-point runs: {pattern['recent_three_point_runs']}\n")
                    f.write(f"  Break points in set: {pattern['break_points_in_set']}\n")
                    f.write(f"  Current momentum: {pattern['current_momentum']}\n\n")

                # Write summary statistics
                f.write("\nSummary Statistics:\n")
                f.write("-" * 80 + "\n\n")

                p1_code = self.player1_code.text()
                p2_code = self.player2_code.text()

                p1_games = [g for g in self.predictor.game_analyses if g.server == p1_code]
                p2_games = [g for g in self.predictor.game_analyses if g.server == p2_code]

                f.write(f"{p1_code} serving games: {len(p1_games)}\n")
                f.write(f"  Holds: {sum(1 for g in p1_games if g.server_won)}\n")
                f.write(f"  Broken: {sum(1 for g in p1_games if not g.server_won)}\n\n")

                f.write(f"{p2_code} serving games: {len(p2_games)}\n")
                f.write(f"  Holds: {sum(1 for g in p2_games if g.server_won)}\n")
                f.write(f"  Broken: {sum(1 for g in p2_games if not g.server_won)}\n\n")

                # Write raw input data for reference
                f.write("\nRaw Input Data:\n")
                f.write("-" * 80 + "\n")
                f.write(self.match_data.toPlainText())

            QMessageBox.information(self, "Export Complete", f"Analysis exported to {filename}")

        except Exception as e:
            self.show_error(f"Export failed: {str(e)}")

    def create_statistics_tab(self):
        """Create the prediction statistics tab with modern design"""
        widget = QWidget()
        main_layout = QVBoxLayout(widget)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)

        # Create a scroll area for better organization
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        scroll_widget = QWidget()
        layout = QVBoxLayout(scroll_widget)
        layout.setSpacing(20)

        # === HEADER SECTION ===
        header_layout = QHBoxLayout()

        # Title
        title_label = QLabel("📊 Prediction Statistics Dashboard")
        title_label.setFont(QFont("Inter", 16, QFont.Bold))
        title_label.setStyleSheet(f"color: {PRIMARY_TEXT}; margin-bottom: 10px;")
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        # Quick stats
        self.quick_stats_label = QLabel("Loading...")
        self.quick_stats_label.setFont(QFont("Inter", 10))
        self.quick_stats_label.setStyleSheet(f"color: {PRIMARY_TEXT}; padding: 5px;")
        header_layout.addWidget(self.quick_stats_label)

        layout.addLayout(header_layout)

        # === OVERVIEW CARDS SECTION ===
        cards_layout = QHBoxLayout()
        cards_layout.setSpacing(15)

        # Overall Performance Card
        overall_card = self.create_stats_card("📈 Overall Performance", "overall")
        cards_layout.addWidget(overall_card)

        # AI vs Math Comparison Card
        comparison_card = self.create_stats_card("🤖 AI vs Math", "comparison")
        cards_layout.addWidget(comparison_card)

        # Pressure Situations Card
        pressure_card = self.create_stats_card("⚡ Pressure Situations", "pressure")
        cards_layout.addWidget(pressure_card)

        layout.addLayout(cards_layout)

        # === DETAILED TABLES SECTION ===
        tables_layout = QHBoxLayout()
        tables_layout.setSpacing(15)

        # Left Column - Score Statistics
        left_column = QVBoxLayout()

        score_group = self.create_modern_table_group("🎯 Performance by Score", "score_stats")
        left_column.addWidget(score_group)

        set_group = self.create_modern_table_group("🎾 Performance by Set", "set_stats")
        left_column.addWidget(set_group)

        # Tournament Level Statistics
        tournament_group = self.create_modern_table_group("🏆 Tournament Level Statistics", "tournament_stats")
        left_column.addWidget(tournament_group)

        # Right Column - AI vs Math & Recent
        right_column = QVBoxLayout()

        ai_math_group = self.create_modern_table_group("🤖 AI vs Mathematical Comparison", "ai_math")
        right_column.addWidget(ai_math_group)

        recent_group = self.create_modern_table_group("📋 Recent Predictions", "recent")
        right_column.addWidget(recent_group)

        tables_layout.addLayout(left_column)
        tables_layout.addLayout(right_column)

        layout.addLayout(tables_layout)

        # === INSIGHTS SECTION ===
        insights_group = QGroupBox()
        insights_group.setTitle("🔍 Prediction Insights & Patterns")
        insights_group.setStyleSheet(f"""
            QGroupBox {{
                font-family: 'Inter', sans-serif;
                font-weight: bold;
                font-size: 12px;
                color: {PRIMARY_TEXT};
                background-color: {PRIMARY_BG};
                border: 2px solid {BORDER_COLOR};
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: {PRIMARY_TEXT};
                background-color: {PRIMARY_BG};
            }}
        """)
        insights_layout = QVBoxLayout(insights_group)

        # Add the patterns text to insights group
        self.patterns_text = QTextEdit()
        self.patterns_text.setReadOnly(True)
        self.patterns_text.setMinimumHeight(200)
        self.patterns_text.setStyleSheet(f"""
            QTextEdit {{
                background-color: {PRIMARY_BG};
                border: 1px solid {BORDER_COLOR};
                border-radius: 6px;
                padding: 10px;
                font-family: 'Inter', sans-serif;
                font-size: 11px;
                line-height: 1.4;
                color: {PRIMARY_TEXT};
            }}
        """)
        insights_layout.addWidget(self.patterns_text)

        layout.addWidget(insights_group)

        # === CONTROL BUTTONS ===
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)

        refresh_button = QPushButton("🔄 Refresh Statistics")
        refresh_button.clicked.connect(self.refresh_statistics)
        refresh_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {PRIMARY_INTERACTIVE};
                color: {PRIMARY_TEXT};
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-family: 'Inter', sans-serif;
                font-weight: bold;
                font-size: 11px;
            }}
            QPushButton:hover {{
                background-color: {SECONDARY_INTERACTIVE};
            }}
        """)
        button_layout.addWidget(refresh_button)

        export_stats_button = QPushButton("📊 Export Statistics")
        export_stats_button.clicked.connect(self.export_statistics)
        export_stats_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {SUCCESS_COLOR};
                color: {PRIMARY_TEXT};
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-family: 'Inter', sans-serif;
                font-weight: bold;
                font-size: 11px;
            }}
            QPushButton:hover {{
                background-color: {PRIMARY_INTERACTIVE};
            }}
        """)
        button_layout.addWidget(export_stats_button)

        clear_history_button = QPushButton("🗑️ Clear All History")
        clear_history_button.clicked.connect(self.clear_prediction_history)
        clear_history_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {ERROR_COLOR};
                color: {PRIMARY_TEXT};
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-family: 'Inter', sans-serif;
                font-weight: bold;
                font-size: 11px;
            }}
            QPushButton:hover {{
                background-color: #CC3333;
            }}
        """)
        button_layout.addWidget(clear_history_button)

        clear_pending_button = QPushButton("⏳ Clear Pending")
        clear_pending_button.clicked.connect(self.clear_pending_predictions)
        clear_pending_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {WARNING_COLOR};
                color: {PRIMARY_TEXT};
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-family: 'Inter', sans-serif;
                font-weight: bold;
                font-size: 11px;
            }}
            QPushButton:hover {{
                background-color: {SECONDARY_INTERACTIVE};
            }}
        """)
        button_layout.addWidget(clear_pending_button)

        button_layout.addStretch()
        layout.addLayout(button_layout)

        # Set the scroll widget and return
        scroll_area.setWidget(scroll_widget)
        main_layout.addWidget(scroll_area)

        return widget

    def create_stats_card(self, title, card_type):
        """Create a modern statistics card"""
        card = QGroupBox()
        card.setFixedHeight(120)
        card.setStyleSheet(f"""
            QGroupBox {{
                background-color: {PRIMARY_BG};
                border: 1px solid {BORDER_COLOR};
                border-radius: 8px;
                margin: 5px;
                padding: 10px;
                font-family: 'Inter', sans-serif;
            }}
        """)

        layout = QVBoxLayout(card)
        layout.setSpacing(5)

        # Title
        title_label = QLabel(title)
        title_label.setFont(QFont("Inter", 11, QFont.Bold))
        title_label.setStyleSheet(f"color: {PRIMARY_TEXT}; margin-bottom: 5px;")
        layout.addWidget(title_label)

        # Content area
        if card_type == "overall":
            self.overall_stats_text = QLabel("Loading...")
            self.overall_stats_text.setStyleSheet(f"color: {PRIMARY_TEXT}; font-size: 10px;")
            self.overall_stats_text.setWordWrap(True)
            layout.addWidget(self.overall_stats_text)
        elif card_type == "comparison":
            self.comparison_stats_text = QLabel("Loading...")
            self.comparison_stats_text.setStyleSheet(f"color: {PRIMARY_TEXT}; font-size: 10px;")
            self.comparison_stats_text.setWordWrap(True)
            layout.addWidget(self.comparison_stats_text)
        elif card_type == "pressure":
            self.pressure_stats_text = QLabel("Loading...")
            self.pressure_stats_text.setStyleSheet(f"color: {PRIMARY_TEXT}; font-size: 10px;")
            self.pressure_stats_text.setWordWrap(True)
            layout.addWidget(self.pressure_stats_text)

        return card

    def create_modern_table_group(self, title, table_type):
        """Create a modern table group with styling"""
        group = QGroupBox(title)
        group.setStyleSheet(f"""
            QGroupBox {{
                font-family: 'Inter', sans-serif;
                font-weight: bold;
                font-size: 12px;
                color: {PRIMARY_TEXT};
                background-color: {PRIMARY_BG};
                border: 2px solid {BORDER_COLOR};
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: {PRIMARY_TEXT};
                background-color: {PRIMARY_BG};
            }}
        """)

        layout = QVBoxLayout(group)

        if table_type == "score_stats":
            self.score_stats_table = QTableWidget()
            self.score_stats_table.setColumnCount(8)
            self.score_stats_table.setHorizontalHeaderLabels([
                "Score", "Total", "Correct", "Wrong", "Pending",
                "Accuracy", "Avg Conf", "Conf when ✓"
            ])
            self.score_stats_table.setMaximumHeight(200)
            self.apply_table_styling(self.score_stats_table)
            layout.addWidget(self.score_stats_table)

        elif table_type == "set_stats":
            self.set_stats_table = QTableWidget()
            self.set_stats_table.setColumnCount(10)
            self.set_stats_table.setHorizontalHeaderLabels([
                "Set", "Total", "Correct", "Wrong", "Pending",
                "Accuracy", "Tied Acc.", "Top Scores", "Avg Conf", "Conf when ✓"
            ])
            self.set_stats_table.setMaximumHeight(200)
            self.apply_table_styling(self.set_stats_table)
            layout.addWidget(self.set_stats_table)

        elif table_type == "tournament_stats":
            self.tournament_stats_table = QTableWidget()
            self.tournament_stats_table.setColumnCount(8)
            self.tournament_stats_table.setHorizontalHeaderLabels([
                "Tournament", "Total", "Correct", "Accuracy", "AI Acc%", "Math Acc%", "Advantage", "Avg Conf"
            ])
            self.tournament_stats_table.setMaximumHeight(150)
            self.apply_table_styling(self.tournament_stats_table)
            layout.addWidget(self.tournament_stats_table)

        elif table_type == "ai_math":
            # AI vs Math comparison in a cleaner format
            self.ai_vs_math_score_table = QTableWidget()
            self.ai_vs_math_score_table.setColumnCount(7)
            self.ai_vs_math_score_table.setHorizontalHeaderLabels([
                "Score", "AI Total", "AI Acc%", "Math Total", "Math Acc%", "Winner", "Diff%"
            ])
            self.ai_vs_math_score_table.setMaximumHeight(150)
            self.apply_table_styling(self.ai_vs_math_score_table)
            layout.addWidget(self.ai_vs_math_score_table)

            self.ai_vs_math_set_table = QTableWidget()
            self.ai_vs_math_set_table.setColumnCount(7)
            self.ai_vs_math_set_table.setHorizontalHeaderLabels([
                "Set", "AI Total", "AI Acc%", "Math Total", "Math Acc%", "Winner", "Diff%"
            ])
            self.ai_vs_math_set_table.setMaximumHeight(120)
            self.apply_table_styling(self.ai_vs_math_set_table)
            layout.addWidget(self.ai_vs_math_set_table)

        elif table_type == "recent":
            self.recent_predictions_table = QTableWidget()
            self.recent_predictions_table.setColumnCount(9)
            self.recent_predictions_table.setHorizontalHeaderLabels([
                "Date", "Set", "Score", "Type", "Predicted", "Actual", "Result", "Confidence", "Action"
            ])
            self.recent_predictions_table.setMaximumHeight(200)
            self.apply_table_styling(self.recent_predictions_table)
            layout.addWidget(self.recent_predictions_table)

        return group

    def apply_table_styling(self, table):
        """Apply modern styling to tables"""
        table.setStyleSheet(f"""
            QTableWidget {{
                background-color: {PRIMARY_BG};
                border: 1px solid {BORDER_COLOR};
                border-radius: 6px;
                gridline-color: {BORDER_COLOR};
                font-family: 'Inter', sans-serif;
                font-size: 12px;
                color: {PRIMARY_TEXT};
            }}
            QTableWidget::item {{
                padding: 4px 8px;
                border-bottom: 1px solid {BORDER_COLOR};
                background-color: {PRIMARY_BG};
                color: {PRIMARY_TEXT};
            }}
            QTableWidget::item:selected {{
                background-color: {SECONDARY_INTERACTIVE};
                color: {PRIMARY_TEXT};
            }}
            QHeaderView::section {{
                background-color: {PRIMARY_INTERACTIVE};
                color: {PRIMARY_TEXT};
                padding: 6px 8px;
                border: none;
                border-bottom: 2px solid {BORDER_COLOR};
                font-family: 'Inter', sans-serif;
                font-weight: bold;
                font-size: 9px;
            }}
        """)

        header = table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)

    def record_outcome(self, winner):
        """Record the actual outcome of a prediction"""
        if not self.current_prediction:
            return

        player1_code = self.player1_code.text()
        player2_code = self.player2_code.text()

        actual_winner = player1_code if winner == 1 else player2_code

        # Get the current score from the current prediction
        current_score = self.current_prediction.score

        # Update ALL pending predictions for this score (both AI and Math) to compare systems
        # This allows comparison between AI and Mathematical predictions
        pending_predictions = []
        for pred in self.tracker.predictions:
            if (pred.actual_winner is None and
                pred.score == current_score and
                pred.player1_code == player1_code and
                pred.player2_code == player2_code):
                pending_predictions.append(pred)
                self.tracker.update_prediction_outcome(pred, actual_winner)

        print(f"🎯 Updated {len(pending_predictions)} pending predictions for score {current_score}")
        for pred in pending_predictions:
            prediction_type = "AI" if pred.is_ai_prediction else "Mathematical"
            correct = "✓" if pred.predicted_winner == actual_winner else "✗"
            print(f"   {correct} {prediction_type}: {pred.predicted_winner} -> {actual_winner}")

        # Update EWMA weights for mathematical predictions only
        math_predictions = [p for p in pending_predictions if not p.is_ai_prediction]
        if math_predictions:
            math_pred = math_predictions[0]  # Should only be one math prediction
            prediction_correct = (math_pred.predicted_winner == actual_winner)
            score = math_pred.score
            set_number = math_pred.set_number

            # Determine who was serving for this prediction
            # Get the last game to find next server
            if self.predictor.game_analyses:
                last_server = self.predictor.game_analyses[-1].server
                next_server = player2_code if last_server == player1_code else player1_code
            else:
                next_server = player1_code

            # Update EWMA weights
            self.predictor.update_ewma_weights_with_result(
                prediction_correct, next_server, player1_code, player2_code,
                score, set_number
            )

        # Show results for all updated predictions
        ai_predictions = [p for p in pending_predictions if p.is_ai_prediction]
        math_predictions = [p for p in pending_predictions if not p.is_ai_prediction]

        result_parts = []

        # Check AI predictions and record for adaptive learning
        if ai_predictions:
            ai_pred = ai_predictions[0]
            prediction_correct = (ai_pred.predicted_winner == actual_winner)

            if prediction_correct:
                result_parts.append(f"<span style='color: {SUCCESS_COLOR}'>✓ Correct AI prediction!</span>")
            else:
                result_parts.append(f"<span style='color: {ERROR_COLOR}'>✗ Incorrect AI prediction</span>")

            # Mark match as completed when outcome is recorded
            session_id = getattr(self, 'current_session_id', None) or "default_session"

            # Update match status to completed
            if hasattr(self.tracker, 'update_match_status'):
                self.tracker.update_match_status(session_id, "completed")

            # Mark session as completed if we have a match manager and valid session
            if hasattr(self, 'match_manager') and self.current_session_id:
                self.match_manager.mark_session_completed(self.current_session_id)

            print(f"✅ Match marked as completed for learning: {session_id}")

            # Record outcome for enhanced learning system
            try:
                # Update coordinator with accuracy information
                try:
                    from learning_system_coordinator import learning_coordinator
                    # Calculate current accuracy for coordinator
                    recent_ai_predictions = [p for p in self.tracker.predictions
                                           if getattr(p, 'is_ai_prediction', False) and p.actual_winner][-50:]
                    if recent_ai_predictions:
                        recent_accuracy = sum(1 for p in recent_ai_predictions
                                            if p.predicted_winner == p.actual_winner) / len(recent_ai_predictions)
                        learning_coordinator.update_accuracy(recent_accuracy)
                        learning_coordinator.increment_prediction_counters()
                except ImportError:
                    pass

                if ENHANCED_LEARNING_AVAILABLE:
                    # Use enhanced learning system
                    # Try to get tournament info from current match info first
                    current_info = getattr(self, 'current_match_info', {})
                    tournament_name = current_info.get('tournament_name', '')

                    # Try to get tournament level from prediction metadata (more reliable)
                    tournament_level = 'Mixed'  # Default fallback

                    # Check prediction's learning metadata first
                    if hasattr(ai_pred, 'learning_metadata') and ai_pred.learning_metadata:
                        tournament_level = ai_pred.learning_metadata.get('tournament_level', tournament_level)

                    # Check prediction's context factors as backup
                    elif hasattr(ai_pred, 'context_factors') and ai_pred.context_factors:
                        match_context = ai_pred.context_factors.get('match_context', {})
                        tournament_level = match_context.get('tournament_level', tournament_level)

                    # Finally, check current match info as last resort
                    else:
                        tournament_level = current_info.get('tournament_level', tournament_level)

                    additional_context = {
                        'tournament_level': tournament_level,
                        'surface': current_info.get('surface', 'Hard'),
                        'set_number': current_info.get('set_number', 1)
                    }

                    result = enhanced_gui_integration['record_prediction_outcome'](
                        prediction_record=ai_pred,
                        tournament_name=tournament_name,
                        additional_context=additional_context
                    )

                    print(f"✓ Enhanced learning: {result.get('tournament_level_used', 'Unknown')} - {result.get('prediction_added', False)}")
                else:
                    # Fallback to original adaptive learning
                    if hasattr(ai_pred, 'prompt_weights') and ai_pred.prompt_weights:
                        from adaptive_learning_system import WeightConfiguration
                        weights_config = WeightConfiguration.from_dict(ai_pred.prompt_weights)

                        gemini_analyzer.record_prediction_outcome(
                            prediction_context=ai_pred.context_factors or {},
                            actual_winner=actual_winner,
                            predicted_winner=ai_pred.predicted_winner,
                            weights_used=weights_config
                        )

                        # Also record in the learning system directly
                        gemini_analyzer.weights_manager.learning_system.record_prediction_outcome(ai_pred)

            except Exception as e:
                print(f"Error recording learning outcome: {e}")

            # Record outcome for enhanced learning system
            try:
                prediction_id = None

                # First try to get prediction_id from current_ai_prediction (most reliable)
                if hasattr(self, 'current_ai_prediction') and self.current_ai_prediction:
                    prediction_id = self.current_ai_prediction.get('prediction_id')
                    print(f"🔍 Found prediction_id in current_ai_prediction: {prediction_id}")

                # Fallback to ai_pred object
                if not prediction_id:
                    if isinstance(ai_pred, dict):
                        prediction_id = ai_pred.get('prediction_id')
                    elif hasattr(ai_pred, 'prediction_id'):
                        prediction_id = ai_pred.prediction_id
                    print(f"🔍 Fallback prediction_id from ai_pred: {prediction_id}")

                if prediction_id:
                    enhanced_gemini_analyzer.record_enhanced_prediction_outcome(
                        actual_winner=actual_winner,
                        prediction_id=prediction_id
                    )
                    print(f"✅ Enhanced learning outcome recorded for prediction {prediction_id}")
                else:
                    print("⚠️ No prediction_id found for enhanced learning outcome recording")
                    print(f"   current_ai_prediction keys: {list(self.current_ai_prediction.keys()) if hasattr(self, 'current_ai_prediction') and self.current_ai_prediction else 'None'}")
                    print(f"   ai_pred type: {type(ai_pred)}")
                    if hasattr(ai_pred, '__dict__'):
                        print(f"   ai_pred attributes: {list(ai_pred.__dict__.keys())}")
            except Exception as e:
                print(f"Error recording enhanced learning outcome: {e}")
                import traceback
                traceback.print_exc()

        # Check mathematical predictions
        if math_predictions:
            math_pred = math_predictions[0]
            if math_pred.predicted_winner == actual_winner:
                result_parts.append(f"<span style='color: {SUCCESS_COLOR}'>✓ Correct mathematical prediction!</span>")
            else:
                result_parts.append(f"<span style='color: {ERROR_COLOR}'>✗ Incorrect mathematical prediction</span>")

        result_text = " | ".join(result_parts) if result_parts else "Outcome recorded"

        self.current_pred_info_label.setText(
            f"{result_text} - Actual winner: {actual_winner}"
        )

        # Disable buttons
        self.player1_win_button.setEnabled(False)
        self.player2_win_button.setEnabled(False)

        # Mark that we just recorded an outcome for this specific score
        self.outcome_just_recorded = True
        self.last_recorded_score = current_score

        # Clear current prediction and AI prediction
        self.current_prediction = None
        self.current_ai_prediction = None

        # Update quick stats
        self.update_quick_stats()

        # Refresh statistics if tab is open
        self.refresh_statistics()

    def update_quick_stats(self):
        """Update the quick statistics display"""
        stats = self.tracker.get_overall_statistics()
        ai_vs_math = self.tracker.get_ai_vs_mathematical_statistics()

        if stats['total_predictions'] == 0:
            if hasattr(self, 'quick_stats_label'):
                self.quick_stats_label.setText("No predictions recorded yet")
            return

        text = f"📊 {stats['total_predictions']} Total | "
        text += f"🎯 {stats['accuracy']:.1f}% Overall | "
        text += f"🤖 AI: {ai_vs_math['ai_predictions']['accuracy']:.1f}% ({ai_vs_math['ai_predictions']['total']}) | "
        text += f"📊 Math: {ai_vs_math['mathematical_predictions']['accuracy']:.1f}% ({ai_vs_math['mathematical_predictions']['total']}) | "
        text += f"⏳ {stats['pending']} Pending"

        if hasattr(self, 'quick_stats_label'):
            self.quick_stats_label.setText(text)

    def on_tab_changed(self, index):
        """Handle tab change events to refresh displays as needed."""
        # Tab indices: 0=Input, 1=SetPrediction, 2=Analysis, 3=LiveMomentum, 4=NextGame, 5=Statistics, 6=MatchManager, 7=DataPersistence

        # Refresh set prediction tab when switching to it
        if index == 1:  # Set Prediction tab
            # Re-synchronize EWMA with tracker
            if hasattr(self.predictor, '_sync_ewma_with_tracker'):
                self.predictor._sync_ewma_with_tracker()

            # Refresh the set prediction display if data is available
            player1_name = self.player1_name.text()
            player1_code = self.player1_code.text()
            player2_name = self.player2_name.text()
            player2_code = self.player2_code.text()

            if player1_code and player2_code and self.predictor.game_analyses:
                # Set auto-analysis mode to prevent creating predictions during tab refresh
                self.auto_analysis_mode = True
                try:
                    self.update_set_prediction_tab(player1_name, player1_code, player2_name, player2_code)
                finally:
                    self.auto_analysis_mode = False

        # Refresh statistics tab when switching to it
        elif index == 5:  # Statistics tab
            self.refresh_statistics()



    def refresh_statistics(self):
        """Refresh all statistics displays with modern card-based design"""
        # Get statistics
        overall = self.tracker.get_overall_statistics()
        ai_vs_math = self.tracker.get_ai_vs_mathematical_statistics()

        # Update Overall Performance Card
        if hasattr(self, 'overall_stats_text'):
            overall_text = f"Total: {overall['total_predictions']} | Completed: {overall['completed']}\n"
            overall_text += f"Overall Accuracy: {overall['accuracy']:.1f}%\n"
            overall_text += f"Tied Scores: {overall['tied_score_accuracy']:.1f}% ({overall['tied_score_total']})"
            self.overall_stats_text.setText(overall_text)

        # Update AI vs Math Comparison Card
        if hasattr(self, 'comparison_stats_text'):
            ai_stats = ai_vs_math['ai_predictions']
            math_stats = ai_vs_math['mathematical_predictions']

            if ai_stats['total'] > 0 and math_stats['total'] > 0:
                if ai_stats['accuracy'] > math_stats['accuracy']:
                    leader = f"🤖 AI Leading: {ai_stats['accuracy']:.1f}%"
                    diff = ai_stats['accuracy'] - math_stats['accuracy']
                    comparison_text = f"{leader}\nAdvantage: +{diff:.1f}%\n"
                elif math_stats['accuracy'] > ai_stats['accuracy']:
                    leader = f"📊 Math Leading: {math_stats['accuracy']:.1f}%"
                    diff = math_stats['accuracy'] - ai_stats['accuracy']
                    comparison_text = f"{leader}\nAdvantage: +{diff:.1f}%\n"
                else:
                    comparison_text = f"🤝 Tied Performance\nBoth: {ai_stats['accuracy']:.1f}%\n"

                comparison_text += f"AI: {ai_stats['total']} | Math: {math_stats['total']}"
            else:
                comparison_text = "Insufficient data for comparison"

            self.comparison_stats_text.setText(comparison_text)

        # Update Pressure Situations Card
        if hasattr(self, 'pressure_stats_text'):
            tied_accuracy = overall['tied_score_accuracy']
            tied_total = overall['tied_score_total']

            if tied_total > 0:
                if tied_accuracy >= 60:
                    status = "🔥 Excellent"
                elif tied_accuracy >= 50:
                    status = "✅ Good"
                elif tied_accuracy >= 40:
                    status = "⚠️ Average"
                else:
                    status = "❌ Needs Work"

                pressure_text = f"{status}\nTied Score Accuracy: {tied_accuracy:.1f}%\n"
                pressure_text += f"Pressure Situations: {tied_total}"
            else:
                pressure_text = "No pressure situations\nrecorded yet"

            self.pressure_stats_text.setText(pressure_text)

        # Update quick stats
        self.update_quick_stats()

        # Statistics by score
        score_stats = self.tracker.get_statistics_by_score()

        # Sort scores for display
        tied_scores = ['3-3', '4-4', '5-5', '6-6']
        other_scores = sorted([s for s in score_stats.keys() if s not in tied_scores])
        all_scores = tied_scores + other_scores

        self.score_stats_table.setRowCount(len(score_stats))
        row = 0

        for score in all_scores:
            if score in score_stats:
                stats = score_stats[score]
                self.score_stats_table.setItem(row, 0, QTableWidgetItem(score))
                self.score_stats_table.setItem(row, 1, QTableWidgetItem(str(stats['total'])))
                self.score_stats_table.setItem(row, 2, QTableWidgetItem(str(stats['correct'])))
                self.score_stats_table.setItem(row, 3, QTableWidgetItem(str(stats['incorrect'])))
                self.score_stats_table.setItem(row, 4, QTableWidgetItem(str(stats['pending'])))
                self.score_stats_table.setItem(row, 5, QTableWidgetItem(f"{stats['accuracy']:.1f}%"))
                self.score_stats_table.setItem(row, 6, QTableWidgetItem(f"{stats['avg_confidence']:.1f}%"))
                self.score_stats_table.setItem(row, 7, QTableWidgetItem(
                    f"{stats['confidence_when_correct']:.1f}%" if stats['confidence_when_correct'] > 0 else "N/A"
                ))

                # Color code tied scores
                if score in tied_scores:
                    for col in range(8):
                        self.score_stats_table.item(row, col).setBackground(QColor(132, 99, 255))  # Light purple for tied scores

                row += 1

        # Statistics by set number
        set_stats = self.tracker.get_statistics_by_set()
        self.set_stats_table.setRowCount(len(set_stats))

        row = 0
        for set_num in sorted(set_stats.keys()):
            stats = set_stats[set_num]
            self.set_stats_table.setItem(row, 0, QTableWidgetItem(f"Set {set_num}"))
            self.set_stats_table.setItem(row, 1, QTableWidgetItem(str(stats['total'])))
            self.set_stats_table.setItem(row, 2, QTableWidgetItem(str(stats['correct'])))
            self.set_stats_table.setItem(row, 3, QTableWidgetItem(str(stats['incorrect'])))
            self.set_stats_table.setItem(row, 4, QTableWidgetItem(str(stats['pending'])))

            # Show accuracy only if there are completed predictions
            if stats['correct'] + stats['incorrect'] > 0:
                self.set_stats_table.setItem(row, 5, QTableWidgetItem(f"{stats['accuracy']:.1f}%"))
                self.set_stats_table.setItem(row, 6, QTableWidgetItem(f"{stats['tied_score_accuracy']:.1f}%"))
            else:
                self.set_stats_table.setItem(row, 5, QTableWidgetItem("N/A"))
                self.set_stats_table.setItem(row, 6, QTableWidgetItem("N/A"))

            # Get top scores for this set
            if 'score_breakdown' in stats and stats['score_breakdown']:
                # Sort scores by frequency
                score_items = sorted(stats['score_breakdown'].items(),
                                   key=lambda x: x[1]['total'], reverse=True)[:3]
                top_scores = []
                for score, data in score_items:
                    if data['accuracy'] > 0:
                        top_scores.append(f"{score} ({data['total']})")
                    else:
                        top_scores.append(f"{score} ({data['total']})")
                self.set_stats_table.setItem(row, 7, QTableWidgetItem(", ".join(top_scores)))
            else:
                self.set_stats_table.setItem(row, 7, QTableWidgetItem("N/A"))

            self.set_stats_table.setItem(row, 8, QTableWidgetItem(f"{stats['avg_confidence']:.1f}%"))
            self.set_stats_table.setItem(row, 9, QTableWidgetItem(
                f"{stats['confidence_when_correct']:.1f}%" if stats['confidence_when_correct'] > 0 else "N/A"
            ))

            # Highlight deciding sets (3rd in Bo3, 5th in Bo5)
            if set_num == 3 or set_num == 5:
                for col in range(10):
                    if self.set_stats_table.item(row, col):
                        self.set_stats_table.item(row, col).setBackground(QColor(255, 68, 68))  # Error color for deciding sets

            row += 1

        # Tournament Level Statistics
        tournament_stats = self.tracker.get_statistics_by_tournament_level()
        tournament_levels = ['ATP', 'Challenger', 'WTA', 'Mixed']

        # Filter to only show tournament levels that have data
        available_levels = [level for level in tournament_levels if level in tournament_stats and tournament_stats[level]['total'] > 0]

        self.tournament_stats_table.setRowCount(len(available_levels))

        for row, tournament_level in enumerate(available_levels):
            stats = tournament_stats[tournament_level]

            # Tournament level with icon
            tournament_icons = {
                'ATP': '🏆',
                'Challenger': '🥈',
                'WTA': '👑',
                'Mixed': '🎾'
            }
            tournament_display = f"{tournament_icons.get(tournament_level, '🎾')} {tournament_level}"

            self.tournament_stats_table.setItem(row, 0, QTableWidgetItem(tournament_display))
            self.tournament_stats_table.setItem(row, 1, QTableWidgetItem(str(stats['total'])))
            self.tournament_stats_table.setItem(row, 2, QTableWidgetItem(str(stats['correct'])))
            self.tournament_stats_table.setItem(row, 3, QTableWidgetItem(f"{stats['accuracy']:.1f}%"))

            # AI vs Math accuracies
            ai_acc = stats['ai_predictions']['accuracy'] if stats['ai_predictions']['total'] > 0 else 0
            math_acc = stats['math_predictions']['accuracy'] if stats['math_predictions']['total'] > 0 else 0

            self.tournament_stats_table.setItem(row, 4, QTableWidgetItem(f"{ai_acc:.1f}%" if ai_acc > 0 else "N/A"))
            self.tournament_stats_table.setItem(row, 5, QTableWidgetItem(f"{math_acc:.1f}%" if math_acc > 0 else "N/A"))

            # Determine advantage
            if ai_acc > 0 and math_acc > 0:
                if ai_acc > math_acc:
                    advantage = f"AI +{ai_acc - math_acc:.1f}%"
                    advantage_item = QTableWidgetItem(advantage)
                    advantage_item.setBackground(QColor(132, 99, 255))  # Light purple for AI advantage
                elif math_acc > ai_acc:
                    advantage = f"Math +{math_acc - ai_acc:.1f}%"
                    advantage_item = QTableWidgetItem(advantage)
                    advantage_item.setBackground(QColor(255, 68, 68))  # Error color for Math advantage
                else:
                    advantage = "Tied"
                    advantage_item = QTableWidgetItem(advantage)
                    advantage_item.setBackground(QColor(112, 84, 221))  # Main purple for tied
                self.tournament_stats_table.setItem(row, 6, advantage_item)
            else:
                self.tournament_stats_table.setItem(row, 6, QTableWidgetItem("N/A"))

            # Average confidence
            self.tournament_stats_table.setItem(row, 7, QTableWidgetItem(f"{stats['avg_confidence']:.1f}%"))

            # Color code by tournament level
            level_colors = {
                'ATP': QColor(132, 99, 255),      # Light purple for ATP
                'Challenger': QColor(112, 84, 221), # Main purple for Challenger
                'WTA': QColor(255, 68, 68),      # Error color for WTA
                'Mixed': QColor(112, 84, 221)     # Main purple for Mixed
            }

            if tournament_level in level_colors:
                for col in range(8):
                    if self.tournament_stats_table.item(row, col):
                        current_color = self.tournament_stats_table.item(row, col).background()
                        if current_color == QColor():  # No color set
                            self.tournament_stats_table.item(row, col).setBackground(level_colors[tournament_level])

        # Recent predictions with indices
        recent_with_indices = self.tracker.get_recent_predictions(20)
        self.recent_predictions_table.setRowCount(len(recent_with_indices))

        for i, (actual_index, pred) in enumerate(recent_with_indices):
            self.recent_predictions_table.setItem(i, 0, QTableWidgetItem(pred.timestamp[:10]))
            self.recent_predictions_table.setItem(i, 1, QTableWidgetItem(f"Set {pred.set_number}"))
            self.recent_predictions_table.setItem(i, 2, QTableWidgetItem(f"{pred.score[0]}-{pred.score[1]}"))

            # Add prediction type column
            pred_type = "AI" if pred.is_ai_prediction else "Math"
            self.recent_predictions_table.setItem(i, 3, QTableWidgetItem(pred_type))

            self.recent_predictions_table.setItem(i, 4, QTableWidgetItem(pred.predicted_winner))
            self.recent_predictions_table.setItem(i, 5, QTableWidgetItem(pred.actual_winner or "Pending"))

            if pred.actual_winner:
                if pred.predicted_winner == pred.actual_winner:
                    result = "✓"
                    color = QColor(132, 99, 255)  # Light purple for correct
                else:
                    result = "✗"
                    color = QColor(255, 68, 68)  # Error color for incorrect
                self.recent_predictions_table.setItem(i, 6, QTableWidgetItem(result))
                for col in range(8):  # Updated to 8 columns before action
                    if self.recent_predictions_table.item(i, col):
                        self.recent_predictions_table.item(i, col).setBackground(color)
            else:
                self.recent_predictions_table.setItem(i, 6, QTableWidgetItem("?"))

            self.recent_predictions_table.setItem(i, 7, QTableWidgetItem(f"{pred.confidence:.1f}%"))

            # Add delete button
            delete_button = QPushButton("Delete")
            delete_button.setMaximumWidth(80)
            delete_button.clicked.connect(lambda checked, idx=actual_index: self.delete_prediction(idx))
            self.recent_predictions_table.setCellWidget(i, 8, delete_button)

        # Update AI vs Mathematical Comparison
        self.update_ai_vs_math_comparison()

        # Update Prediction Patterns and Insights
        self.update_prediction_patterns()

    def update_ai_vs_math_comparison(self):
        """Update AI vs Mathematical comparison tables"""
        # AI vs Math by Score
        score_stats = self.tracker.get_ai_vs_math_by_score()

        # Sort scores for display (tied scores first)
        tied_scores = ['3-3', '4-4', '5-5', '6-6']
        other_scores = sorted([s for s in score_stats.keys() if s not in tied_scores])
        all_scores = [s for s in tied_scores if s in score_stats] + other_scores

        self.ai_vs_math_score_table.setRowCount(len(all_scores))

        for row, score in enumerate(all_scores):
            stats = score_stats[score]
            ai_stats = stats['ai']
            math_stats = stats['math']

            self.ai_vs_math_score_table.setItem(row, 0, QTableWidgetItem(score))
            self.ai_vs_math_score_table.setItem(row, 1, QTableWidgetItem(str(ai_stats['total'])))
            self.ai_vs_math_score_table.setItem(row, 2, QTableWidgetItem(f"{ai_stats['accuracy']:.1f}%" if ai_stats['total'] > 0 else "N/A"))
            self.ai_vs_math_score_table.setItem(row, 3, QTableWidgetItem(str(math_stats['total'])))
            self.ai_vs_math_score_table.setItem(row, 4, QTableWidgetItem(f"{math_stats['accuracy']:.1f}%" if math_stats['total'] > 0 else "N/A"))

            # Determine winner and difference
            if ai_stats['total'] > 0 and math_stats['total'] > 0:
                if ai_stats['accuracy'] > math_stats['accuracy']:
                    winner = "AI"
                    difference = ai_stats['accuracy'] - math_stats['accuracy']
                    winner_item = QTableWidgetItem(winner)
                    winner_item.setBackground(QColor(132, 99, 255))  # Light purple for AI wins
                elif math_stats['accuracy'] > ai_stats['accuracy']:
                    winner = "Math"
                    difference = math_stats['accuracy'] - ai_stats['accuracy']
                    winner_item = QTableWidgetItem(winner)
                    winner_item.setBackground(QColor(255, 68, 68))  # Error color for Math wins
                else:
                    winner = "Tie"
                    difference = 0
                    winner_item = QTableWidgetItem(winner)
                    winner_item.setBackground(QColor(112, 84, 221))  # Main purple for ties

                self.ai_vs_math_score_table.setItem(row, 5, winner_item)
                self.ai_vs_math_score_table.setItem(row, 6, QTableWidgetItem(f"{difference:+.1f}%"))
            else:
                self.ai_vs_math_score_table.setItem(row, 5, QTableWidgetItem("N/A"))
                self.ai_vs_math_score_table.setItem(row, 6, QTableWidgetItem("N/A"))

            # Highlight tied scores
            if score in tied_scores:
                for col in range(7):
                    if self.ai_vs_math_score_table.item(row, col):
                        current_color = self.ai_vs_math_score_table.item(row, col).background()
                        if current_color == QColor():  # No color set
                            self.ai_vs_math_score_table.item(row, col).setBackground(QColor(132, 99, 255))  # Light purple for tied scores

        # AI vs Math by Set
        set_stats = self.tracker.get_ai_vs_math_by_set()
        sorted_sets = sorted(set_stats.keys())

        self.ai_vs_math_set_table.setRowCount(len(sorted_sets))

        for row, set_num in enumerate(sorted_sets):
            stats = set_stats[set_num]
            ai_stats = stats['ai']
            math_stats = stats['math']

            self.ai_vs_math_set_table.setItem(row, 0, QTableWidgetItem(f"Set {set_num}"))
            self.ai_vs_math_set_table.setItem(row, 1, QTableWidgetItem(str(ai_stats['total'])))
            self.ai_vs_math_set_table.setItem(row, 2, QTableWidgetItem(f"{ai_stats['accuracy']:.1f}%" if ai_stats['total'] > 0 else "N/A"))
            self.ai_vs_math_set_table.setItem(row, 3, QTableWidgetItem(str(math_stats['total'])))
            self.ai_vs_math_set_table.setItem(row, 4, QTableWidgetItem(f"{math_stats['accuracy']:.1f}%" if math_stats['total'] > 0 else "N/A"))

            # Determine winner and difference
            if ai_stats['total'] > 0 and math_stats['total'] > 0:
                if ai_stats['accuracy'] > math_stats['accuracy']:
                    winner = "AI"
                    difference = ai_stats['accuracy'] - math_stats['accuracy']
                    winner_item = QTableWidgetItem(winner)
                    winner_item.setBackground(QColor(132, 99, 255))  # Light purple for AI wins
                elif math_stats['accuracy'] > ai_stats['accuracy']:
                    winner = "Math"
                    difference = math_stats['accuracy'] - ai_stats['accuracy']
                    winner_item = QTableWidgetItem(winner)
                    winner_item.setBackground(QColor(255, 68, 68))  # Error color for Math wins
                else:
                    winner = "Tie"
                    difference = 0
                    winner_item = QTableWidgetItem(winner)
                    winner_item.setBackground(QColor(112, 84, 221))  # Main purple for ties

                self.ai_vs_math_set_table.setItem(row, 5, winner_item)
                self.ai_vs_math_set_table.setItem(row, 6, QTableWidgetItem(f"{difference:+.1f}%"))
            else:
                self.ai_vs_math_set_table.setItem(row, 5, QTableWidgetItem("N/A"))
                self.ai_vs_math_set_table.setItem(row, 6, QTableWidgetItem("N/A"))

            # Highlight deciding sets (3rd in Bo3, 5th in Bo5)
            if set_num == 3 or set_num == 5:
                for col in range(7):
                    if self.ai_vs_math_set_table.item(row, col):
                        current_color = self.ai_vs_math_set_table.item(row, col).background()
                        if current_color == QColor():  # No color set
                            self.ai_vs_math_set_table.item(row, col).setBackground(QColor(255, 68, 68))  # Error color for deciding sets

    def update_prediction_patterns(self):
        """Update prediction patterns and insights"""
        patterns = self.tracker.get_prediction_patterns()

        if not patterns:
            self.patterns_text.setPlainText("No prediction data available for pattern analysis.")
            return

        insights_text = "🔍 PREDICTION PATTERNS & INSIGHTS\n\n"

        # Most predicted scores - simplified format
        insights_text += "📊 Most Predicted Scores:\n"
        if patterns.get('ai_most_predicted_scores'):
            ai_scores = [score for score, count in patterns['ai_most_predicted_scores'][:3]]
            insights_text += f"  AI best scores: {', '.join(ai_scores)}\n"
        if patterns.get('math_most_predicted_scores'):
            math_scores = [score for score, count in patterns['math_most_predicted_scores'][:3]]
            insights_text += f"  Math best scores: {', '.join(math_scores)}\n"

        insights_text += "\n"

        # Most predicted sets - simplified format
        insights_text += "🎾 Most Predictive Sets:\n"
        if patterns.get('ai_most_predicted_sets'):
            ai_sets = [f"Set {set_num}" for set_num, count in patterns['ai_most_predicted_sets'][:3]]
            insights_text += f"  AI: {', '.join(ai_sets)}\n"
        if patterns.get('math_most_predicted_sets'):
            math_sets = [f"Set {set_num}" for set_num, count in patterns['math_most_predicted_sets'][:3]]
            insights_text += f"  Math: {', '.join(math_sets)}\n"

        insights_text += "\n"

        # Confidence vs Accuracy Analysis - simplified
        insights_text += "🎯 Performance Analysis:\n"

        ai_conf = patterns.get('ai_confidence_accuracy', {})
        math_conf = patterns.get('math_confidence_accuracy', {})

        # Show only the most relevant confidence levels
        ai_high = ai_conf.get('high_confidence', {})
        math_high = math_conf.get('high_confidence', {})

        if ai_high.get('count', 0) > 0:
            insights_text += f"  AI High Confidence: {ai_high['count']} predictions, {ai_high['accuracy']:.1f}% accuracy\n"
        if math_high.get('count', 0) > 0:
            insights_text += f"  Math High Confidence: {math_high['count']} predictions, {math_high['accuracy']:.1f}% accuracy\n"

        insights_text += "\n"

        # Tied vs Non-Tied Score Performance - simplified
        insights_text += "⚖️ Pressure Situations (Tied Scores):\n"
        score_perf = patterns.get('score_type_performance', {})

        if score_perf:
            ai_tied = score_perf.get('ai_tied_scores', {})
            math_tied = score_perf.get('math_tied_scores', {})

            if ai_tied.get('count', 0) > 0:
                insights_text += f"  AI Tied Scores: {ai_tied['accuracy']:.1f}% accuracy ({ai_tied['count']} predictions)\n"
            if math_tied.get('count', 0) > 0:
                insights_text += f"  Math Tied Scores: {math_tied['accuracy']:.1f}% accuracy ({math_tied['count']} predictions)\n"

        # Key Insights - simplified and actionable
        insights_text += "\n💡 Summary:\n"

        # Overall performance comparison
        overall_stats = self.tracker.get_ai_vs_mathematical_statistics()
        ai_overall = overall_stats['ai_predictions']
        math_overall = overall_stats['mathematical_predictions']

        if ai_overall['total'] > 0 and math_overall['total'] > 0:
            if ai_overall['accuracy'] > math_overall['accuracy']:
                diff = ai_overall['accuracy'] - math_overall['accuracy']
                insights_text += f"  • AI leads by {diff:.1f}% ({ai_overall['accuracy']:.1f}% vs {math_overall['accuracy']:.1f}%)\n"
            elif math_overall['accuracy'] > ai_overall['accuracy']:
                diff = math_overall['accuracy'] - ai_overall['accuracy']
                insights_text += f"  • Math leads by {diff:.1f}% ({math_overall['accuracy']:.1f}% vs {ai_overall['accuracy']:.1f}%)\n"
            else:
                insights_text += f"  • Both systems tied at {ai_overall['accuracy']:.1f}% accuracy\n"

        # Add recommendation based on performance
        if ai_overall['total'] > 0 and math_overall['total'] > 0:
            if ai_overall['accuracy'] > math_overall['accuracy'] + 5:
                insights_text += f"  • Recommendation: Prefer AI predictions\n"
            elif math_overall['accuracy'] > ai_overall['accuracy'] + 5:
                insights_text += f"  • Recommendation: Prefer Math predictions\n"
            else:
                insights_text += f"  • Recommendation: Both systems performing similarly\n"

        self.patterns_text.setPlainText(insights_text)

    def export_statistics(self):
        """Export statistics to file"""
        filename, _ = QFileDialog.getSaveFileName(
            self, "Export Statistics", "prediction_statistics.txt", "Text Files (*.txt)"
        )

        if filename:
            self.tracker.export_statistics(filename)
            QMessageBox.information(self, "Export Complete", f"Statistics exported to {filename}")

    def clear_prediction_history(self):
        """Clear all prediction history"""
        reply = QMessageBox.question(
            self, "Clear All History",
            "Are you sure you want to clear ALL prediction history (including completed predictions)? This cannot be undone.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.tracker.clear_history()
            # Also clear EWMA accuracy history
            self.predictor.ewma_weights.accuracy_history = []
            self.predictor.ewma_weights.prediction_count = 0
            self.predictor.ewma_weights.save_weights()
            self.refresh_statistics()
            self.update_quick_stats()
            QMessageBox.information(self, "History Cleared", "All prediction history has been cleared.")

    def clear_pending_predictions(self):
        """Clear only pending predictions"""
        # First check if there are any pending predictions
        stats = self.tracker.get_overall_statistics()
        pending_count = stats['pending']

        if pending_count == 0:
            QMessageBox.information(self, "No Pending Predictions", "There are no pending predictions to clear.")
            return

        reply = QMessageBox.question(
            self, "Clear Pending Predictions",
            f"Are you sure you want to clear {pending_count} pending prediction(s)? This cannot be undone.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Get pending predictions before clearing to clean EWMA if needed
            pending_predictions = [p for p in self.tracker.predictions if p.actual_winner is None]

            cleared = self.tracker.clear_pending_predictions()

            # Note: Pending predictions don't affect EWMA history since they have no outcomes
            # EWMA only tracks completed predictions with actual results

            self.refresh_statistics()
            self.update_quick_stats()

            # Clear current prediction if it exists
            if self.current_prediction and self.current_prediction.actual_winner is None:
                self.current_prediction = None
                self.current_pred_info_label.setText("No prediction to record")
                self.player1_win_button.setEnabled(False)
                self.player2_win_button.setEnabled(False)

            QMessageBox.information(self, "Pending Cleared", f"Cleared {pending_count} pending prediction(s).")

    def delete_prediction_from_ai_systems(self, pred):
        """Systematically remove prediction from all AI learning systems"""
        deletion_summary = {}

        print(f"🗑️ Removing prediction from all AI systems...")
        print(f"   Score: {pred.score}, Set: {pred.set_number}, Time: {pred.timestamp}")

        # Check if this is an AI prediction - only AI predictions should be deleted from AI systems
        is_ai_prediction = getattr(pred, 'is_ai_prediction', False)
        if not is_ai_prediction:
            print(f"   Skipping AI systems deletion - this is not an AI prediction")
            return deletion_summary

        # Check if this prediction has a prediction_id (preferred method)
        prediction_id = getattr(pred, 'prediction_id', None)
        if prediction_id:
            print(f"   Using prediction_id: {prediction_id}")
        else:
            print(f"   No prediction_id found, using fallback method")

        # 1. Enhanced Adaptive Learning System
        try:
            from enhanced_adaptive_learning_system import EnhancedAdaptiveLearningSystem
            # Create a fresh instance to ensure we have the latest data
            enhanced_learning_system = EnhancedAdaptiveLearningSystem()

            if prediction_id:
                # Use the new ID-based deletion method (more reliable)
                deleted_count = enhanced_learning_system.delete_prediction_by_id(prediction_id)

                # If ID-based deletion failed, fall back to criteria-based
                if deleted_count == 0:
                    print(f"   ID-based deletion failed, falling back to criteria-based")
                    deleted_count = enhanced_learning_system.delete_prediction_by_criteria(
                        pred.score, pred.set_number, pred.timestamp
                    )
            else:
                # Fallback to criteria-based deletion
                deleted_count = enhanced_learning_system.delete_prediction_by_criteria(
                    pred.score, pred.set_number, pred.timestamp
                )

            deletion_summary['Enhanced Learning System'] = deleted_count
            print(f"   Enhanced Learning System: {deleted_count} record(s) removed")

            # Force refresh learning-eligible count after deletion
            if deleted_count > 0:
                current_eligible = len(enhanced_learning_system.get_learning_eligible_predictions())
                print(f"   📊 Enhanced system learning-eligible count after deletion: {current_eligible}")

        except Exception as e:
            deletion_summary['Enhanced Learning System'] = f"Error: {str(e)}"
            print(f"   Enhanced Learning System: Error - {e}")

        # 2. Enhanced Learning System V2
        try:
            from enhanced_adaptive_learning_v2 import enhanced_learning_system_v2
            deleted_count = enhanced_learning_system_v2.delete_prediction_by_criteria(
                pred.score, pred.set_number, pred.timestamp
            )
            deletion_summary['Enhanced Learning V2'] = deleted_count
            print(f"   Enhanced Learning V2: {deleted_count} record(s) removed")
        except Exception as e:
            deletion_summary['Enhanced Learning V2'] = f"Error: {str(e)}"
            print(f"   Enhanced Learning V2: Error - {e}")

        # 3. Adaptive Learning System (JSON-based)
        try:
            from adaptive_learning_system import AdaptiveLearningSystem
            learning_system = AdaptiveLearningSystem()
            # This system is primarily JSON-based and might not have a deletion method.
            if hasattr(learning_system, 'delete_prediction_by_criteria'):
                deleted_count = learning_system.delete_prediction_by_criteria(
                    pred.score, pred.set_number, pred.timestamp
                )
                print(f"   Adaptive Learning System: {deleted_count} record(s) removed")
            else:
                deleted_count = 0
                print(f"   Adaptive Learning System: JSON-only, no deletion method required.")
            deletion_summary['Adaptive Learning System'] = deleted_count
        except Exception as e:
            deletion_summary['Adaptive Learning System'] = f"Error: {str(e)}"
            print(f"   Adaptive Learning System: Error - {e}")

        # 4. EWMA Weights Cleanup
        try:
            if pred.actual_winner is not None:
                # Find and remove the corresponding entry from EWMA history
                original_count = len(self.predictor.ewma_weights.accuracy_history)
                self.predictor.ewma_weights.accuracy_history = [
                    h for h in self.predictor.ewma_weights.accuracy_history
                    if not (h['context']['score'][0] == pred.score[0] and
                           h['context']['score'][1] == pred.score[1] and
                           h['context']['set_number'] == pred.set_number and
                           abs((datetime.fromisoformat(h['timestamp']) -
                               datetime.fromisoformat(pred.timestamp)).total_seconds()) < 60)
                ]
                removed_count = original_count - len(self.predictor.ewma_weights.accuracy_history)
                self.predictor.ewma_weights.save_weights()
                deletion_summary['EWMA Weights'] = f"Updated ({removed_count} removed)"
                print(f"   EWMA Weights: {removed_count} record(s) removed")
            else:
                deletion_summary['EWMA Weights'] = "N/A (no outcome)"
        except Exception as e:
            deletion_summary['EWMA Weights'] = f"Error: {str(e)}"
            print(f"   EWMA Weights: Error - {e}")

        # The final sync call has been REMOVED from here.
        return deletion_summary

    def delete_prediction(self, index):
        """Delete a specific prediction with comprehensive AI system cleanup"""
        # Get the prediction details before deleting
        pred = self.tracker.get_prediction_by_index(index)
        if not pred:
            return

        # Create enhanced confirmation message with prediction details and AI warning
        pred_info = f"Score: {pred.score[0]}-{pred.score[1]}\n"
        pred_info += f"Date: {pred.timestamp[:10]}\n"
        pred_info += f"Predicted: {pred.predicted_winner}\n"
        pred_info += f"Actual: {pred.actual_winner or 'Pending'}\n"
        pred_info += f"Confidence: {pred.confidence:.1f}%\n\n"
        pred_info += "⚠️ WARNING: This will also remove the prediction from ALL AI learning systems:\n"
        pred_info += "• Enhanced Adaptive Learning System\n"
        pred_info += "• Enhanced Learning System V2\n"
        pred_info += "• Adaptive Learning Database\n"
        pred_info += "• EWMA Weights History"

        reply = QMessageBox.question(
            self, "Delete Prediction - AI Systems Cleanup",
            f"Are you sure you want to delete this prediction?\n\n{pred_info}",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Check if this is the current pending prediction
            if self.current_prediction and pred.timestamp == self.current_prediction.timestamp:
                self.current_prediction = None
                self.current_pred_info_label.setText("No prediction to record")
                self.player1_win_button.setEnabled(False)
                self.player2_win_button.setEnabled(False)

            # Step 1: Remove from all AI systems (without the final sync)
            deletion_summary = self.delete_prediction_from_ai_systems(pred)

            # Step 2: Delete from the main tracker (this saves prediction_history.json)
            # Try ID-based deletion first if prediction_id is available
            prediction_id = getattr(pred, 'prediction_id', None)
            if prediction_id:
                main_tracker_deleted = self.tracker.delete_prediction_by_id(prediction_id)
            else:
                # Fallback to index-based deletion
                main_tracker_deleted = self.tracker.delete_prediction(index)

            if main_tracker_deleted:
                # Step 3: Get accurate final count without complex reinitialization
                final_counts = {}
                try:
                    # Simple approach: Create fresh instance to get accurate count
                    from enhanced_adaptive_learning_system import EnhancedAdaptiveLearningSystem
                    fresh_enhanced_system = EnhancedAdaptiveLearningSystem()
                    enhanced_eligible = fresh_enhanced_system.get_learning_eligible_predictions()

                    # Get main tracker count (should be accurate since we just deleted from it)
                    main_ai_completed = [p for p in self.tracker.predictions
                                       if (getattr(p, 'is_ai_prediction', False) and
                                           p.actual_winner and
                                           getattr(p, 'match_status', None) == 'completed')]

                    final_counts = {
                        'enhanced_system_eligible': len(enhanced_eligible),
                        'main_system_completed': len(main_ai_completed)
                    }

                    print("✅ Final state after deletion:")
                    print(f"   📊 Enhanced system learning-eligible count: {final_counts.get('enhanced_system_eligible', 'N/A')}")
                    print(f"   📊 Main system completed count: {final_counts.get('main_system_completed', 'N/A')}")

                except Exception as e:
                    print(f"⚠️ Error getting final counts after deletion: {e}")

                # Step 4: Refresh GUI and show accurate confirmation
                self.refresh_statistics()
                self.update_quick_stats()

                summary_text = "Prediction deleted successfully!\n\nRemoved from:\n"
                for system, result in deletion_summary.items():
                    if isinstance(result, int):
                        summary_text += f"• {system}: {result} record(s)\n"
                    else:
                        summary_text += f"• {system}: {result}\n"

                summary_text += "\n--- Final State ---\n"
                summary_text += f"✅ New Learning-Eligible Count: {final_counts.get('enhanced_system_eligible', 'N/A')}"

                QMessageBox.information(self, "Deletion Complete", summary_text)
            else:
                QMessageBox.warning(self, "Error", "Failed to delete prediction from main tracker.")

    def _reinitialize_learning_systems(self):
        """Reinitialize learning system components after a reset or deletion to prevent stale data."""
        try:
            print("🔄 Reinitializing learning systems to sync with file changes...")

            # Reinitialize the main prediction tracker
            self.tracker = PredictionTracker()
            self.predictor.tracker = self.tracker  # Update the predictor's tracker instance

            # Reinitialize key learning and analysis singletons by reloading their modules
            import sys
            import importlib

            modules_to_reload = [
                'enhanced_adaptive_learning_system',
                'enhanced_adaptive_learning_v2',
                'adaptive_learning_system',
                'prediction_weights_manager',
                'enhanced_gemini_integration',
                'money_making_betting_system',
            ]

            for module_name in modules_to_reload:
                if module_name in sys.modules:
                    importlib.reload(sys.modules[module_name])

            # This ensures the global instances are the fresh ones from the reloaded modules
            global enhanced_gemini_analyzer

            # Re-assign the reloaded instances
            from enhanced_gemini_integration import enhanced_gemini_analyzer
            from money_making_betting_system import MoneyMakingBettingSystem
            self.betting_system = MoneyMakingBettingSystem()
            self.betting_system.load_betting_data()

            print("✅ Learning systems reinitialized successfully.")
        except Exception as e:
            print(f"❌ Error during learning system reinitialization: {e}")
            QMessageBox.warning(self, "Reinitialization Warning",
                                f"Could not fully re-sync all systems after deletion: {e}\n\n"
                                "A restart of the application is recommended for full consistency.")
















    def check_gemini_api_status(self):
        """Check and update Gemini API status"""
        if config.is_gemini_configured():
            self.ai_status_label.setText("API configured")
            self.ai_status_label.setStyleSheet(f"color: {SUCCESS_COLOR};")
            self.ai_analyze_button.setEnabled(True)
            # Hide the API key if it's already set
            if config.get_gemini_api_key():
                self.api_key_input.setText("••••••••••••••••")
                # Ensure the gemini_analyzer is configured with the loaded API key
                gemini_analyzer.set_api_key(config.get_gemini_api_key())
        else:
            self.ai_status_label.setText("API key required")
            self.ai_status_label.setStyleSheet(f"color: {WARNING_COLOR};")
            self.ai_analyze_button.setEnabled(False)

    def set_gemini_api_key(self):
        """Set the Gemini API key"""
        api_key = self.api_key_input.text().strip()
        if not api_key or api_key == "••••••••••••••••":
            QMessageBox.warning(self, "Invalid API Key", "Please enter a valid Gemini API key.")
            return

        # Test the API key
        success = gemini_analyzer.set_api_key(api_key)
        if success:
            # Save to config with persistence option
            config.set_gemini_api_key(api_key, save_to_file=self.persist_api_key_checkbox.isChecked())
            QMessageBox.information(self, "Success", "Gemini API key configured successfully!")
            self.check_gemini_api_status()
        else:
            QMessageBox.critical(self, "Error", "Failed to configure Gemini API. Please check your API key.")

    def toggle_api_key_persistence(self, checked: bool):
        """Toggle API key persistence setting"""
        config.set_persist_api_keys(checked)

        if checked and config.get_gemini_api_key():
            # If enabling persistence and we have an API key, save it
            config.save_config(include_api_keys=True)
        elif not checked:
            # If disabling persistence, remove API key from file
            config.save_config(include_api_keys=False)

    def auto_load_last_session(self):
        """Auto-load the last active session if enabled"""
        if not config.get_auto_load_last_session():
            return

        try:
            active_session = self.match_manager.get_active_session()
            if active_session and not active_session.is_completed:
                # Load the session silently
                session_data = self.match_manager.load_session_to_gui(active_session.session_id)
                if session_data:
                    self.restore_session_data(session_data)
                    print(f"Auto-loaded session: {active_session.get_display_name()}")
        except Exception as e:
            print(f"Failed to auto-load last session: {e}")

    def toggle_monitor_checking(self, state):
        """Toggle automatic monitor checking"""
        self.monitor_enabled = state == Qt.Checked

        if self.monitor_enabled:
            # Start the timer if it's not already running
            if not hasattr(self, 'monitor_check_timer') or not self.monitor_check_timer.isActive():
                self.monitor_check_timer = QTimer()
                self.monitor_check_timer.timeout.connect(self.check_for_monitor_data)
                self.monitor_check_timer.start(5000)
            print("✅ Automatic monitor checking enabled")
        else:
            # Stop the timer
            if hasattr(self, 'monitor_check_timer') and self.monitor_check_timer.isActive():
                self.monitor_check_timer.stop()
            print("⏹️ Automatic monitor checking disabled")

    def show_settings_dialog(self):
        """Show application settings dialog"""
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QCheckBox

        dialog = QDialog(self)
        dialog.setWindowTitle("Application Settings")
        dialog.setModal(True)
        dialog.resize(400, 200)

        layout = QVBoxLayout(dialog)

        # API Key persistence setting
        api_persistence_layout = QHBoxLayout()
        api_persistence_checkbox = QCheckBox("Remember API Key between sessions")
        api_persistence_checkbox.setChecked(config.get_persist_api_keys())
        api_persistence_layout.addWidget(api_persistence_checkbox)
        layout.addLayout(api_persistence_layout)

        # Auto-load last session setting
        auto_load_layout = QHBoxLayout()
        auto_load_checkbox = QCheckBox("Auto-load last session on startup")
        auto_load_checkbox.setChecked(config.get_auto_load_last_session())
        auto_load_layout.addWidget(auto_load_checkbox)
        layout.addLayout(auto_load_layout)

        # Fullscreen preference setting
        fullscreen_layout = QHBoxLayout()
        fullscreen_checkbox = QCheckBox("Launch in fullscreen mode")
        fullscreen_checkbox.setChecked(config.get('launch_fullscreen', True))
        fullscreen_layout.addWidget(fullscreen_checkbox)
        layout.addLayout(fullscreen_layout)

        # Buttons
        button_layout = QHBoxLayout()
        save_button = QPushButton("Save")
        cancel_button = QPushButton("Cancel")

        def save_settings():
            config.set_persist_api_keys(api_persistence_checkbox.isChecked())
            config.set_auto_load_last_session(auto_load_checkbox.isChecked())
            config.set('launch_fullscreen', fullscreen_checkbox.isChecked())
            config.save_config()
            self.persist_api_key_checkbox.setChecked(api_persistence_checkbox.isChecked())
            dialog.accept()

        save_button.clicked.connect(save_settings)
        cancel_button.clicked.connect(dialog.reject)

        button_layout.addWidget(save_button)
        button_layout.addWidget(cancel_button)
        layout.addLayout(button_layout)

        dialog.exec_()

    def restore_session_data(self, session_data: dict):
        """Restore GUI state from session data"""
        try:
            # Restore player information
            self.player1_name.setText(session_data.get('player1_name', ''))
            self.player1_code.setText(session_data.get('player1_code', ''))
            self.player2_name.setText(session_data.get('player2_name', ''))
            self.player2_code.setText(session_data.get('player2_code', ''))

            # Restore match settings
            match_format = session_data.get('match_format', 'Best of 3')
            format_index = 0 if match_format == "Best of 3" else 1
            self.match_format.setCurrentIndex(format_index)

            # Restore set number
            set_number = session_data.get('current_set', 1)
            self.set_number.setCurrentIndex(set_number - 1)

            # Restore surface
            surface = session_data.get('surface', 'Hard')
            surface_items = ["Hard", "Clay", "Grass", "Indoor Hard"]
            if surface in surface_items:
                self.surface_type.setCurrentIndex(surface_items.index(surface))

            # Restore tournament information
            tournament_level = session_data.get('tournament_level', 'ATP')
            tournament_items = ["ATP", "Challenger", "WTA", "Mixed"]
            if tournament_level in tournament_items:
                self.tournament_level.setCurrentText(tournament_level)

            tournament_name = session_data.get('tournament_name', '')
            self.tournament_name.setText(tournament_name)

            # Restore starting server
            starting_server = session_data.get('starting_server', '')
            player1_code = session_data.get('player1_code', '')
            player2_code = session_data.get('player2_code', '')

            if starting_server == player1_code:
                self.set_starting_server(1)
            elif starting_server == player2_code:
                self.set_starting_server(2)
            else:
                self.set_starting_server(1)  # Default to Player 1

            # Restore favorite information
            favorite_player = session_data.get('favorite_player', 0)
            self.set_favorite(favorite_player)

            favorite_odds = session_data.get('favorite_odds')
            if favorite_odds:
                self.favorite_odds.setText(str(favorite_odds))

            # Restore previous sets
            previous_sets = session_data.get('previous_sets', [])
            for i, winner_code in enumerate(previous_sets):
                if i < len(self.prev_set_winners):
                    winner_name = self.get_full_name_from_code(winner_code)
                    if winner_name:
                        _, combo = self.prev_set_winners[i]
                        index = combo.findText(winner_name)
                        if index >= 0:
                            combo.setCurrentIndex(index)

            # Restore point-by-point data
            point_data = session_data.get('point_by_point_data', '')
            self.match_data.setPlainText(point_data)

            # Update current session ID
            if hasattr(self.match_manager, 'active_session_id'):
                self.current_session_id = self.match_manager.active_session_id

            print(f"Session data restored successfully")

        except Exception as e:
            print(f"Error restoring session data: {e}")

    def get_ai_analysis(self):
        """Get AI analysis for the current set prediction"""
        if not hasattr(self, 'predictor') or not self.predictor.game_analyses:
            QMessageBox.warning(self, "No Data", "Please analyze a match first.")
            return

        if len(self.predictor.game_analyses) < 6:
            QMessageBox.warning(self, "Insufficient Data", "At least 6 games must be played for AI analysis.")
            return

        # Get current match data
        player1_name = self.player1_name.text().strip()
        player1_code = self.player1_code.text().strip()
        player2_name = self.player2_name.text().strip()
        player2_code = self.player2_code.text().strip()

        if not all([player1_name, player1_code, player2_name, player2_code]):
            QMessageBox.warning(self, "Missing Data", "Player information is incomplete.")
            return

        # Check for missing player data and prompt for download
        from player_data_uploader import PlayerDataUploader
        missing_players, available_players = PlayerDataUploader.check_missing_players(
            player1_name, player2_name
        )

        if missing_players:
            # Use automatic download system
            proceed = automatic_player_download(missing_players, self)
            if not proceed:
                return

        # Calculate current score
        p1_games = sum(1 for g in self.predictor.game_analyses if g.winner == player1_code)
        p2_games = sum(1 for g in self.predictor.game_analyses if g.winner == player2_code)
        current_score = (p1_games, p2_games)

        # Get set number
        set_number = self.set_number.currentIndex() + 1

        # Get serving patterns from detailed analysis (matches GUI display)
        serve_patterns = {}
        if hasattr(self.predictor, 'serve_patterns'):
            analysis = self.predictor.get_detailed_analysis()
            serve_patterns = analysis.get('serve_patterns', {})

        # Get match context including surface information
        match_context = {
            'surface': self.surface_type.currentText(),
            'match_format': self.match_format.currentText()
        }
        if hasattr(self, 'current_match_info'):
            match_context.update(self.current_match_info)

        # Show loading state
        self.ai_analyze_button.setEnabled(False)
        self.ai_analyze_button.setText("Analyzing...")
        self.ai_status_label.setText("Getting AI analysis...")
        self.ai_status_label.setStyleSheet(f"color: {PRIMARY_INTERACTIVE};")

        # Process events to update UI
        QApplication.processEvents()

        try:
            # Set predictor instance for accurate server detection
            gemini_analyzer.set_predictor_instance(self.predictor)

            # Get AI analysis with enhanced learning
            result = enhanced_gemini_analyzer.analyze_set_with_enhanced_learning(
                set_number, current_score, player1_name, player1_code,
                player2_name, player2_code, serve_patterns, match_context
            )

            if result["success"]:
                # Display analysis
                self.ai_analysis_text.setPlainText(result["analysis"])

                # Update probabilities
                p1_prob = result["player1_probability"] * 100
                p2_prob = result["player2_probability"] * 100
                self.ai_player1_prob_label.setText(f"{player1_name}: {p1_prob:.0f}%")
                self.ai_player2_prob_label.setText(f"{player2_name}: {p2_prob:.0f}%")

                # Store AI prediction data for outcome recording
                predicted_winner = player1_code if result["player1_probability"] > result["player2_probability"] else player2_code
                prediction_probability = max(result["player1_probability"], result["player2_probability"])

                self.current_ai_prediction = {
                    'score': current_score,
                    'player1_name': player1_name,
                    'player1_code': player1_code,
                    'player2_name': player2_name,
                    'player2_code': player2_code,
                    'predicted_winner': predicted_winner,
                    'prediction_probability': prediction_probability,
                    'ai_analysis_text': result["analysis"],
                    'ai_model_used': result["model_used"],
                    'set_number': set_number,
                    'match_context': match_context,
                    'adaptive_weights': result.get("adaptive_weights", {}),
                    'prediction_context': result.get("prediction_context", {}),
                    'prediction_id': result.get("prediction_id"),  # Enhanced learning prediction ID
                    'balance_used': result.get("balance_used", {}),
                    'enhanced_learning_version': result.get("enhanced_learning_version")
                }

                # Update status
                self.ai_status_label.setText(f"Analysis complete ({result['model_used']})")
                self.ai_status_label.setStyleSheet(f"color: {SUCCESS_COLOR};")

                # Refresh the set prediction tab to enable outcome recording
                self.update_set_prediction_tab(player1_name, player1_code, player2_name, player2_code)
            else:
                # Show error
                error_msg = result.get("error", "Unknown error occurred")
                self.ai_analysis_text.setPlainText(f"Error: {error_msg}")
                self.ai_player1_prob_label.setText(f"{player1_name}: N/A")
                self.ai_player2_prob_label.setText(f"{player2_name}: N/A")

                # Clear AI prediction data
                self.current_ai_prediction = None

                self.ai_status_label.setText("Analysis failed")
                self.ai_status_label.setStyleSheet(f"color: {ERROR_COLOR};")

        except Exception as e:
            # Handle unexpected errors
            error_msg = f"Unexpected error: {str(e)}"
            self.ai_analysis_text.setPlainText(error_msg)
            self.ai_status_label.setText("Analysis failed")
            self.ai_status_label.setStyleSheet(f"color: {ERROR_COLOR};")

            # Clear AI prediction data
            self.current_ai_prediction = None

        finally:
            # Restore button state
            self.ai_analyze_button.setEnabled(True)
            self.ai_analyze_button.setText("Get AI Analysis")

    def generate_betting_recommendations(self, current_score, set_number, predicted_winner, confidence, set_prediction):
        """Generate enhanced betting recommendations based on current analysis"""
        try:
            # Validate inputs
            if not predicted_winner or predicted_winner == 'Unknown':
                return f"\n\n--- 💰 ENHANCED BETTING ANALYSIS ---\n• ❌ No predicted winner available for betting analysis"

            if not current_score or len(current_score) != 2:
                return f"\n\n--- 💰 ENHANCED BETTING ANALYSIS ---\n• ❌ Invalid score format for betting analysis"

            # Extract surface information
            surface = 'Hard'  # Default
            if hasattr(self, 'surface_var') and self.surface_var.get():
                surface = self.surface_var.get()

            # Extract momentum type from set prediction with enhanced detection
            momentum_type = 'neutral'
            momentum_factors = set_prediction.get('momentum_factors', {})

            # Determine momentum type from the prediction data
            if momentum_factors:
                for player, factors in momentum_factors.items():
                    if player == predicted_winner:
                        # Handle both dict and ServePattern object
                        if isinstance(factors, dict):
                            current_momentum = factors.get('current_momentum', 'neutral')
                        elif hasattr(factors, 'current_momentum'):
                            # It's a ServePattern object
                            current_momentum = factors.current_momentum
                            # Convert enum to string if needed
                            if hasattr(current_momentum, 'value'):
                                current_momentum = current_momentum.value
                            elif hasattr(current_momentum, 'name'):
                                current_momentum = current_momentum.name.lower()
                        else:
                            current_momentum = 'neutral'

                        # Convert to our momentum type with enhanced mapping
                        current_momentum_str = str(current_momentum).lower()
                        if 'strong_serving' in current_momentum_str or 'dominant' in current_momentum_str:
                            momentum_type = 'strong_serving'
                        elif 'weak_serving' in current_momentum_str or 'struggling' in current_momentum_str:
                            momentum_type = 'weak_serving'
                        elif 'momentum_shift' in current_momentum_str or 'shifting' in current_momentum_str:
                            momentum_type = 'momentum_shift'
                        elif 'solid_serving' in current_momentum_str or 'steady' in current_momentum_str:
                            momentum_type = 'solid_serving'
                        break

            # Create enhanced betting opportunity
            score_str = f"{current_score[0]}-{current_score[1]}"
            opportunity = self.betting_system.evaluate_betting_opportunity(
                score=score_str,
                set_number=set_number,
                predicted_player=predicted_winner,
                confidence=confidence,
                momentum_type=momentum_type,
                surface=surface
            )

            # Convert bet size to percentage (Brazilian style)
            stake_percentage = opportunity.bet_size_percentage * 100

            # Generate enhanced betting recommendation text
            betting_text = f"--- 💰 ENHANCED BETTING ANALYSIS ---"

            # Add tier-based recommendation with enhanced formatting
            tier_emojis = {
                'TIER_1_PREMIUM': '🏆',
                'TIER_2_STRONG': '⭐',
                'TIER_3_MODERATE': '🟡',
                'TIER_4_WEAK': '🟢',
                'AVOID': '🔴',
                'AVOID_NO_DATA': '❌'
            }

            tier_emoji = tier_emojis.get(opportunity.recommendation_tier, '❓')

            if stake_percentage > 0:
                # Enhanced tier display
                if opportunity.recommendation_tier == 'TIER_1_PREMIUM':
                    betting_text += f"\n• {tier_emoji} PREMIUM BET: {stake_percentage:.1f}% stake"
                elif opportunity.recommendation_tier == 'TIER_2_STRONG':
                    betting_text += f"\n• {tier_emoji} STRONG BET: {stake_percentage:.1f}% stake"
                elif opportunity.recommendation_tier == 'TIER_3_MODERATE':
                    betting_text += f"\n• {tier_emoji} MODERATE BET: {stake_percentage:.1f}% stake"
                elif opportunity.recommendation_tier == 'TIER_4_WEAK':
                    betting_text += f"\n• {tier_emoji} SMALL BET: {stake_percentage:.1f}% stake"
                elif opportunity.recommendation_tier == 'AVOID_NO_DATA':
                    betting_text += f"\n• {tier_emoji} NO DATA: System reset - need predictions first"
                else:
                    betting_text += f"\n• {tier_emoji} AVOID: Negative expected value"

                # Enhanced metrics display
                betting_text += f"\n• 📊 Expected ROI: {opportunity.expected_roi:.1f}%"
                betting_text += f"\n• ⚖️ Risk Level: {opportunity.risk_level.replace('_', ' ').title()}"
                betting_text += f"\n• 🎯 AI Accuracy: {opportunity.ai_accuracy:.1f}%"

                if opportunity.sample_size > 0:
                    betting_text += f"\n• 📈 Sample Size: {opportunity.sample_size} matches"
            else:
                if opportunity.recommendation_tier == 'AVOID_NO_DATA':
                    betting_text += f"\n• {tier_emoji} RECOMMENDATION: COLLECT DATA FIRST"
                    betting_text += f"\n• 📊 Expected ROI: Cannot calculate without data"
                    betting_text += f"\n• ⚠️ Reason: System reset - make AI predictions to build data"
                else:
                    betting_text += f"\n• 🚫 RECOMMENDATION: SKIP THIS BET"
                    betting_text += f"\n• 📊 Expected ROI: {opportunity.expected_roi:.1f}%"
                    betting_text += f"\n• ⚠️ Reason: Negative expected value"

            # Enhanced reasoning display
            betting_text += f"\n• 💡 Analysis: {opportunity.reasoning}"

            # Enhanced re-betting strategy with more detailed guidance
            if score_str in ['5-5', '6-6']:
                betting_text += f"\n\n--- 🔄 ADVANCED RE-BETTING STRATEGY ---"
                if score_str == '5-5':
                    betting_text += f"\n• 🎯 Critical Point: Next 2 games decide set"
                    betting_text += f"\n• 💪 If momentum stays: HOLD original position"
                    betting_text += f"\n• 🔄 If momentum shifts: Consider 25% hedge"
                    betting_text += f"\n• ⚡ High confidence (>70%): Potential DOUBLE DOWN"
                elif score_str == '6-6':
                    betting_text += f"\n• 🎾 TIEBREAK ALERT: Maximum variance scenario"
                    betting_text += f"\n• 📉 Reduce position size by 30-50%"
                    betting_text += f"\n• 👀 Monitor first serve % closely"
                    betting_text += f"\n• ⚖️ Consider live hedging opportunities"

            # Enhanced strategy for earlier scores
            elif score_str in ['3-3', '4-4'] and set_number >= 2:
                betting_text += f"\n\n--- 🔄 PROGRESSIVE BETTING STRATEGY ---"
                betting_text += f"\n• 📊 Current Position: Mid-set tied scenario"
                betting_text += f"\n• 💎 Strong momentum: HOLD and monitor"
                betting_text += f"\n• 📈 Score progression to 5-5: Re-evaluate"
                betting_text += f"\n• 🎯 Tiebreak approach: Adjust position size"

            # Add general betting tips for all scenarios
            betting_text += f"\n\n--- 💡 BETTING TIPS ---"
            if opportunity.sample_size < 10:
                betting_text += f"\n• ⚠️ Limited historical data - bet conservatively"
            if opportunity.risk_level in ['HIGH_RISK', 'VERY_HIGH_RISK']:
                betting_text += f"\n• 🛡️ High risk scenario - consider smaller stakes"
            if opportunity.ai_accuracy > 65:
                betting_text += f"\n• ✅ Strong AI confidence - favorable scenario"

            # Add data source transparency with reset detection
            data_source = opportunity.context_factors.get('data_source', 'unknown')
            if data_source == 'enhanced_learning':
                betting_text += f"\n• 🤖 Using latest AI learning data"
            elif data_source == 'adaptive_learning':
                betting_text += f"\n• 📊 Using adaptive learning patterns"
            elif data_source == 'insufficient_data':
                betting_text += f"\n• 🔄 SYSTEM RESET DETECTED"
                betting_text += f"\n• 📝 Make AI predictions to rebuild betting data"
                betting_text += f"\n• ⏳ Recommendations will improve with more data"
            else:
                betting_text += f"\n• 📋 Using historical baseline data"

            # Save betting data automatically
            self.betting_system.save_betting_data()

            return betting_text

        except Exception as e:
            return f"\n\n--- 💰 ENHANCED BETTING ANALYSIS ---\n• ❌ Error generating recommendation: {str(e)}\n• 🔧 Please check betting system configuration"

    def update_betting_system_data(self):
        """Update betting system with latest prediction data"""
        try:
            # Check if betting system is available
            if not hasattr(self, 'betting_system') or self.betting_system is None:
                return

            # Reload the latest EWMA weights and performance data
            if hasattr(self, 'predictor') and hasattr(self.predictor, 'ewma_weights'):
                ewma = self.predictor.ewma_weights

                # Update the betting system's performance data with latest EWMA data
                if hasattr(ewma, 'score_set_performance'):
                    for key, perf in ewma.score_set_performance.items():
                        if perf['total'] > 0:
                            accuracy = (perf['correct'] / perf['total']) * 100
                            # Calculate ROI assuming 1.9 odds
                            roi = (accuracy/100 * 1.9) - 1
                            roi_percentage = roi * 100

                            # Update betting system data
                            self.betting_system.score_set_performance[key] = {
                                'accuracy': accuracy,
                                'sample_size': perf['total'],
                                'roi': roi_percentage
                            }

                # Save updated data
                self.betting_system.save_betting_data()

        except Exception as e:
            print(f"Error updating betting system data: {e}")

    def closeEvent(self, event):
        """Handle application close event"""
        # Save any pending data (prediction statistics and adaptive learning are auto-saved)

        # Save match sessions
        if hasattr(self, 'match_manager'):
            try:
                self.match_manager.save_sessions()
            except Exception as e:
                print(f"Error saving match sessions on close: {e}")

        # Save betting system data
        if hasattr(self, 'betting_system'):
            try:
                self.betting_system.save_betting_data()
            except Exception as e:
                print(f"Error saving betting data on close: {e}")

        # Save monitor state
        try:
            self._save_monitor_state()
        except Exception as e:
            print(f"Error saving monitor state on close: {e}")

        # Save adaptive learning system data
        try:
            self._save_adaptive_learning_state()
        except Exception as e:
            print(f"Error saving adaptive learning state on close: {e}")

        event.accept()

    def open_learning_dashboard(self):
        """Open the adaptive learning dashboard"""
        try:
            from learning_metrics_dashboard import LearningMetricsDashboard

            # Create and show learning dashboard window
            self.learning_dashboard_window = LearningMetricsDashboard()
            self.learning_dashboard_window.setWindowTitle("Adaptive Learning System Dashboard")
            self.learning_dashboard_window.resize(800, 600)
            self.learning_dashboard_window.show()

        except ImportError as e:
            QMessageBox.warning(
                self, "Module Not Found",
                f"Learning dashboard module not available: {e}"
            )
        except Exception as e:
            QMessageBox.critical(
                self, "Error",
                f"Failed to open learning dashboard: {e}"
            )

    def optimize_ai_weights(self):
        """Manually trigger AI weight optimization"""
        try:
            from adaptive_learning_system import AdaptiveLearningSystem

            learning_system = AdaptiveLearningSystem()
            result = learning_system.optimize_weights()

            if result.get('status') == 'weights_updated':
                QMessageBox.information(
                    self, "Optimization Complete",
                    f"AI weights optimized successfully!\n\n"
                    f"Previous accuracy: {result['old_accuracy']:.1%}\n"
                    f"Predicted accuracy: {result['predicted_accuracy']:.1%}\n"
                    f"Improvement: {result['improvement']:.1%}\n"
                    f"Confidence: {result['confidence']:.1%}"
                )
            elif result.get('status') == 'insufficient_data':
                QMessageBox.warning(
                    self, "Insufficient Data",
                    f"Need at least {result['required_size']} AI predictions for optimization.\n"
                    f"Currently have {result['sample_size']} predictions.\n\n"
                    f"Continue using the AI analysis feature to collect more data."
                )
            elif result.get('status') == 'no_significant_improvement':
                QMessageBox.information(
                    self, "No Improvement Found",
                    f"No statistically significant improvement found.\n\n"
                    f"Current accuracy: {result['current_accuracy']:.1%}\n"
                    f"Predicted improvement: {result['predicted_improvement']:.1%}\n"
                    f"Confidence: {result['confidence']:.1%}\n\n"
                    f"The system will continue learning from new predictions."
                )
            else:
                QMessageBox.information(
                    self, "Optimization Status",
                    f"Optimization status: {result.get('status', 'Unknown')}\n\n"
                    f"Please check the learning dashboard for more details."
                )

        except ImportError as e:
            QMessageBox.warning(
                self, "Module Not Found",
                f"Adaptive learning system not available: {e}"
            )
        except Exception as e:
            QMessageBox.critical(
                self, "Error",
                f"Failed to optimize weights: {e}"
            )

    def reset_all_weights(self):
        """Reset all adaptive learning weights to default values"""
        reply = QMessageBox.question(
            self, "Reset All Weights",
            "Are you sure you want to reset ALL learning systems to default values?\n\n"
            "This will reset:\n"
            "• Enhanced Learning System V2 (Tournament-specific weights)\n"
            "• Robust Validation System data\n"
            "• Learning System Integration data\n"
            "• Basic adaptive learning weights\n"
            "• Surface-specific weights (Clay, Hard, Grass)\n"
            "• Momentum weights\n"
            "• Enhanced learning balances\n"
            "• Prediction weights manager settings\n"
            "• EWMA weights\n"
            "• All prediction statistics and history\n\n"
            "⚠️ This will clear ALL learning progress and cannot be undone!",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                # Try to import the new comprehensive reset script first
                try:
                    from comprehensive_reset_script_v2 import comprehensive_reset_v2
                    reset_function = comprehensive_reset_v2
                    script_version = "V2 (Enhanced)"
                except ImportError:
                    # Fallback to original script (using backward compatibility function)
                    from comprehensive_reset_script_v2 import comprehensive_reset
                    reset_function = comprehensive_reset
                    script_version = "V1 (Original)"

                # Use QProgressDialog instead of QMessageBox for better control
                from PyQt5.QtWidgets import QProgressDialog
                from PyQt5.QtCore import Qt, QTimer

                progress = QProgressDialog(f"Resetting all learning systems using {script_version}...\n\nThis may take a few moments.", None, 0, 0, self)
                progress.setWindowTitle("Resetting All Learning Systems")
                progress.setWindowModality(Qt.WindowModal)
                progress.setCancelButton(None)  # No cancel button
                progress.setAutoClose(True)
                progress.setAutoReset(True)
                progress.show()

                # Process events to show the dialog
                QApplication.processEvents()

                # Create a safety timer to force close the dialog after 30 seconds
                safety_timer = QTimer()
                safety_timer.setSingleShot(True)
                safety_timer.timeout.connect(lambda: self._force_close_dialog(progress))
                safety_timer.start(30000)  # 30 seconds

                try:
                    # Perform the reset
                    success = reset_function()
                finally:
                    # Stop the safety timer
                    safety_timer.stop()

                    # Aggressively close the progress dialog
                    self._aggressive_dialog_close(progress)

                    # Additional safety: schedule another close attempt
                    QTimer.singleShot(100, lambda: self._aggressive_dialog_close(progress))

                # Show appropriate message based on success
                if success:
                    # Reinitialize learning system components after successful reset
                    try:
                        self._reinitialize_learning_systems()
                        QMessageBox.information(
                            self, "Reset Complete",
                            "✅ All learning systems have been successfully reset to default values!\n\n"
                            "🔄 The system will now use default weights for all predictions.\n"
                            "📊 Tournament-specific learning will start fresh.\n"
                            "🎯 Begin making predictions to build new learning data.\n\n"
                            "💡 Tip: Use the Learning Dashboard to monitor progress!"
                        )
                    except Exception as e:
                        QMessageBox.warning(
                            self, "Reset Complete with Warning",
                            "✅ All learning systems have been reset successfully!\n\n"
                            "⚠️ However, there was an issue reinitializing some components.\n"
                            "Please restart the application to ensure everything works properly.\n\n"
                            f"Technical details: {str(e)}"
                        )
                else:
                    QMessageBox.warning(
                        self, "Reset Partially Complete",
                        "⚠️ Some components were reset successfully, but others failed.\n\n"
                        "Check the console output for detailed information.\n"
                        "You may need to restart the application or manually delete some files."
                    )

            except ImportError as e:
                QMessageBox.warning(
                    self, "Module Not Found",
                    f"Comprehensive reset module not available: {e}\n\n"
                    "Please ensure comprehensive_reset_script_v2.py is in the same directory."
                )
            except Exception as e:
                QMessageBox.critical(
                    self, "Reset Failed",
                    f"Failed to reset learning systems: {e}\n\n"
                    "Please check the console for detailed error information."
                )

    def show_enhanced_learning_status(self):
        """Show enhanced learning system status and analytics"""
        try:
            status = enhanced_gemini_analyzer.get_enhanced_learning_status()

            # Create detailed status message
            status_text = "🧠 Enhanced Adaptive Learning System Status\n\n"

            # Current balance information
            balance_config = status.get('balance_configuration', {})
            status_text += f"📊 Current Balance Configuration:\n"
            status_text += f"• Balance Version: {status.get('current_balance_version', '1.0')}\n"

            # Show set-specific balances
            for set_num in range(1, 4):  # Show first 3 sets
                set_balance = balance_config.get(f'set_{set_num}_balance', {'historical': 0.5, 'momentum': 0.5})
                status_text += f"• Set {set_num}: {set_balance.get('historical', 0.5):.0%} historical / {set_balance.get('momentum', 0.5):.0%} momentum\n"

            status_text += "\n"

            # Learning statistics
            total_predictions = status.get('contextual_predictions_count', 0)
            completed_predictions = status.get('completed_contextual_predictions', 0)
            status_text += f"📈 Learning Statistics:\n"
            status_text += f"• Total Predictions: {total_predictions}\n"
            status_text += f"• Completed Predictions: {completed_predictions}\n"

            # Calculate accuracy if we have completed predictions
            if completed_predictions > 0:
                contextual_analysis = status.get('contextual_analysis', {})
                overall_accuracy = contextual_analysis.get('overall_accuracy', 0)
                status_text += f"• Overall Accuracy: {overall_accuracy:.1%}\n"
            else:
                status_text += f"• Overall Accuracy: N/A (no completed predictions)\n"

            # Get actual minimum from enhanced learning system
            min_sample_size = enhanced_gemini_analyzer.enhanced_learning_system.min_sample_size
            status_text += f"• Minimum for optimization: {min_sample_size} predictions\n\n"

            # Context-specific performance (if available)
            contextual_analysis = status.get('contextual_analysis', {})
            if contextual_analysis:
                # Show performance by set number
                by_set = contextual_analysis.get('by_set_number', {})
                if by_set:
                    status_text += f"🎯 Performance by Set:\n"
                    for set_key, perf in by_set.items():
                        accuracy = perf.get('accuracy', 0)
                        count = perf.get('sample_size', 0)
                        status_text += f"• {set_key.replace('_', ' ').title()}: {accuracy:.1%} ({count} predictions)\n"
                    status_text += "\n"

                # Show performance by surface
                by_surface = contextual_analysis.get('by_surface', {})
                if by_surface:
                    status_text += f"🏟️ Performance by Surface:\n"
                    for surface, perf in by_surface.items():
                        accuracy = perf.get('accuracy', 0)
                        count = perf.get('sample_size', 0)
                        status_text += f"• {surface}: {accuracy:.1%} ({count} predictions)\n"
                    status_text += "\n"

            # Next optimization info with improved logic
            is_ready, message = self.should_show_optimization_ready()
            if is_ready:
                status_text += f"✅ Ready for optimization!\n"
                status_text += f"• Use 'Optimize Enhanced Balances' to trigger optimization\n"
            else:
                status_text += f"⏭️ Next Optimization:\n"
                status_text += f"• {message}\n"

            QMessageBox.information(self, "Enhanced Learning Status", status_text)

        except Exception as e:
            QMessageBox.critical(
                self, "Error",
                f"Failed to get enhanced learning status: {e}"
            )

    def should_show_optimization_ready(self):
        """
        Improved logic for determining if optimization should be offered
        """
        from datetime import datetime, timedelta

        learning_system = enhanced_gemini_analyzer.enhanced_learning_system

        completed_predictions = [p for p in learning_system.contextual_predictions
                               if p.actual_winner is not None]
        total_completed = len(completed_predictions)

        # Must have minimum predictions (use actual minimum from enhanced system)
        min_required = enhanced_gemini_analyzer.enhanced_learning_system.min_sample_size
        if total_completed < min_required:
            return False, f"Need {min_required - total_completed} more predictions"

        # Count predictions since last optimization
        predictions_since_opt = 0
        try:
            last_opt_time_str = learning_system.current_balance.created_at
            if last_opt_time_str:
                last_opt_time = datetime.fromisoformat(last_opt_time_str.replace('Z', '+00:00'))

                for pred in learning_system.contextual_predictions:
                    if pred.actual_winner is not None:
                        pred_time = datetime.fromisoformat(pred.timestamp.replace('Z', '+00:00'))
                        if pred_time.replace(tzinfo=None) > last_opt_time.replace(tzinfo=None):
                            predictions_since_opt += 1
        except:
            predictions_since_opt = total_completed  # If error, assume all are new

        # Need at least 200 new predictions since last optimization (research-based threshold)
        min_new_predictions = 200
        if predictions_since_opt < min_new_predictions:
            return False, f"Need {min_new_predictions - predictions_since_opt} more predictions since last optimization"

        # Optional: Check time since last optimization (prevent too frequent optimization)
        try:
            last_opt_time_str = learning_system.current_balance.created_at
            if last_opt_time_str:
                last_opt_time = datetime.fromisoformat(last_opt_time_str.replace('Z', '+00:00'))
                time_since_opt = datetime.now() - last_opt_time.replace(tzinfo=None)

                # Require at least 30 minutes between optimizations
                if time_since_opt < timedelta(minutes=30):
                    remaining_time = timedelta(minutes=30) - time_since_opt
                    remaining_minutes = int(remaining_time.total_seconds() / 60)
                    return False, f"Wait {remaining_minutes} more minutes before next optimization"
        except:
            pass  # If we can't parse time, don't block optimization

        return True, "Ready for optimization!"

    def force_enhanced_optimization(self):
        """Manually trigger enhanced balance optimization"""
        try:
            result = enhanced_gemini_analyzer.force_balance_optimization()

            if result.get('status') == 'balances_updated':
                improvements_text = ""
                for improvement in result.get('improvements', []):
                    context = improvement.get('context', 'Unknown')
                    accuracy_improvement = improvement.get('accuracy_improvement', 0)
                    improvements_text += f"• {context.replace('_', ' ').title()}: +{accuracy_improvement:.1%}\n"

                QMessageBox.information(
                    self, "Enhanced Optimization Complete",
                    f"✅ Enhanced balances successfully optimized!\n\n"
                    f"New Balance Version: {result.get('new_version', 'Unknown')}\n"
                    f"Improvements Found:\n{improvements_text}\n"
                    f"The system will now use these optimized balances for future predictions."
                )
            elif result.get('status') == 'insufficient_data':
                sample_size = result.get('sample_size', 0)
                required_size = result.get('required_size', enhanced_gemini_analyzer.enhanced_learning_system.min_sample_size)

                QMessageBox.information(
                    self, "Insufficient Data",
                    f"Not enough prediction data for optimization.\n\n"
                    f"Current completed predictions: {sample_size}\n"
                    f"Minimum required: {required_size}\n\n"
                    f"Continue making predictions and recording outcomes to enable optimization."
                )
            elif result.get('status') == 'no_significant_improvements':
                analysis = result.get('analysis', {})
                overall_accuracy = analysis.get('overall_accuracy', 0)
                sample_size = analysis.get('sample_size', 0)

                # Create more detailed message based on accuracy level
                if overall_accuracy >= 0.99:
                    accuracy_note = "🎯 Excellent! Your system is performing at near-perfect accuracy."
                    improvement_note = "With such high accuracy, only minor optimizations are possible."
                elif overall_accuracy >= 0.90:
                    accuracy_note = "✅ Great performance! Your system is highly accurate."
                    improvement_note = "Small improvements may be found with more data."
                else:
                    accuracy_note = "📊 System is learning and improving."
                    improvement_note = "More predictions needed to identify optimization opportunities."

                QMessageBox.information(
                    self, "Optimization Status",
                    f"{accuracy_note}\n\n"
                    f"Current overall accuracy: {overall_accuracy:.1%}\n"
                    f"Sample size: {sample_size} predictions\n\n"
                    f"{improvement_note}\n\n"
                    f"The system will continue learning from new predictions and "
                    f"automatically optimize when beneficial patterns are detected."
                )
            else:
                QMessageBox.information(
                    self, "Enhanced Optimization Status",
                    f"Optimization status: {result.get('status', 'Unknown')}\n\n"
                    f"Please check the enhanced learning status for more details."
                )

        except Exception as e:
            QMessageBox.critical(
                self, "Error",
                f"Failed to optimize enhanced balances: {e}"
            )

    def open_learning_dashboard(self):
        """Open the enhanced learning system dashboard"""
        if not ENHANCED_LEARNING_AVAILABLE:
            QMessageBox.warning(self, "Not Available", "Enhanced Learning System is not available.")
            return

        try:
            import subprocess
            import sys
            subprocess.Popen([sys.executable, "enhanced_learning_dashboard.py"])
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Could not open learning dashboard: {str(e)}")

    def run_system_validation(self):
        """Run comprehensive system validation"""
        if not ENHANCED_LEARNING_AVAILABLE:
            QMessageBox.warning(self, "Not Available", "Enhanced Learning System is not available.")
            return

        try:
            # Show progress dialog
            progress = QProgressBar()
            progress.setRange(0, 0)  # Indeterminate progress
            progress.setWindowTitle("Running Validation...")
            progress.show()
            QApplication.processEvents()

            result = enhanced_gui_integration['run_validation']()
            progress.close()

            if result.get('status') == 'insufficient_data':
                QMessageBox.information(
                    self, "Validation Results",
                    f"Insufficient data for validation.\n\n"
                    f"Current predictions: {result.get('total_predictions', 0)}\n"
                    f"Required minimum: {result.get('minimum_required', 50)}\n\n"
                    f"{result.get('message', '')}"
                )
            else:
                # Show validation results
                validation_result = result.get('validation_result', {})
                overall = validation_result.get('overall_summary', {})
                recommendations = result.get('integration_recommendations', [])

                reliability_score = overall.get('system_reliability_score', 0.0)
                overall_accuracy = overall.get('overall_accuracy', 0.0)
                significant_segments = overall.get('statistically_significant_segments', 0)
                total_segments = overall.get('total_segments_tested', 0)

                # Create detailed message
                message = f"""🔍 VALIDATION RESULTS

📊 Overall Performance:
• Reliability Score: {reliability_score:.2f} ({reliability_score*100:.0f}%)
• Overall Accuracy: {overall_accuracy:.3f} ({overall_accuracy*100:.1f}%)
• Significant Segments: {significant_segments}/{total_segments}

🎯 Recommendations:
"""

                for i, rec in enumerate(recommendations, 1):
                    message += f"{i}. {rec}\n"

                if reliability_score >= 0.6 and overall_accuracy >= 0.55:
                    icon = QMessageBox.Information
                    title = "✅ Validation Successful"
                elif reliability_score >= 0.3:
                    icon = QMessageBox.Warning
                    title = "⚠️ Validation Warning"
                else:
                    icon = QMessageBox.Critical
                    title = "❌ Validation Failed"

                msg_box = QMessageBox(icon, title, message, QMessageBox.Ok, self)
                msg_box.exec_()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Validation failed: {str(e)}")

    def reset_enhanced_learning_v2(self):
        """Reset only the Enhanced Learning System V2"""
        if not ENHANCED_LEARNING_AVAILABLE:
            QMessageBox.warning(self, "Not Available", "Enhanced Learning System V2 is not available.")
            return

        reply = QMessageBox.question(
            self, "Reset Enhanced Learning V2",
            "Are you sure you want to reset the Enhanced Learning System V2?\n\n"
            "This will reset:\n"
            "• Tournament-specific learned weights (ATP/Challenger/WTA)\n"
            "• Surface-specific learning data (Clay/Hard/Grass)\n"
            "• Validation history and learning logs\n"
            "• All segment-specific predictions\n\n"
            "⚠️ This will clear Enhanced Learning V2 progress but keep other systems intact.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                # Use QProgressDialog for better control
                from PyQt5.QtWidgets import QProgressDialog
                from PyQt5.QtCore import Qt, QTimer

                progress = QProgressDialog("Resetting Enhanced Learning V2...\n\nThis may take a few moments.", None, 0, 0, self)
                progress.setWindowTitle("Resetting Enhanced Learning V2")
                progress.setWindowModality(Qt.WindowModal)
                progress.setCancelButton(None)  # No cancel button
                progress.setAutoClose(True)
                progress.setAutoReset(True)
                progress.show()
                QApplication.processEvents()

                # Create a safety timer to force close the dialog after 30 seconds
                safety_timer = QTimer()
                safety_timer.setSingleShot(True)
                safety_timer.timeout.connect(lambda: self._force_close_dialog(progress))
                safety_timer.start(30000)  # 30 seconds

                try:
                    # Reset the enhanced learning system
                    result = enhanced_gui_integration['reset_system']()
                finally:
                    # Stop the safety timer
                    safety_timer.stop()

                    # Aggressively close the progress dialog
                    self._aggressive_dialog_close(progress)

                    # Additional safety: schedule another close attempt
                    QTimer.singleShot(100, lambda: self._aggressive_dialog_close(progress))

                if result.get('status') == 'reset_complete':
                    # Reinitialize learning system components after successful reset
                    try:
                        self._reinitialize_learning_systems()
                        QMessageBox.information(
                            self, "Reset Complete",
                            "✅ Enhanced Learning System V2 has been reset successfully!\n\n"
                            "🔄 Tournament-specific learning will start fresh.\n"
                            "📊 All segment weights have been cleared.\n"
                            "🎯 Begin making predictions to build new learning data.\n\n"
                            "💡 Use the Learning Dashboard to monitor progress!"
                        )
                    except Exception as e:
                        QMessageBox.warning(
                            self, "Reset Complete with Warning",
                            "✅ Enhanced Learning System V2 has been reset successfully!\n\n"
                            "⚠️ However, there was an issue reinitializing some components.\n"
                            "Please restart the application to ensure everything works properly.\n\n"
                            f"Technical details: {str(e)}"
                        )
                else:
                    QMessageBox.warning(
                        self, "Reset Failed",
                        f"Failed to reset Enhanced Learning V2: {result}"
                    )

            except Exception as e:
                QMessageBox.critical(self, "Error", f"Error resetting Enhanced Learning V2: {str(e)}")

    def _reinitialize_learning_systems(self):
        """Reinitialize learning system components after a reset to prevent hanging"""
        try:
            print("🔄 Reinitializing learning systems after reset...")

            # Reinitialize the prediction tracker
            from prediction_tracker import PredictionTracker
            self.tracker = PredictionTracker()

            # Reinitialize the predictor with the new tracker
            from enhanced_predictor import EnhancedTennisPredictor
            self.predictor = EnhancedTennisPredictor(self.tracker)

            # Clear any cached prediction data
            self.current_prediction = None
            self.current_ai_prediction = None
            self.outcome_just_recorded = False
            self.last_recorded_score = None

            # Reinitialize learning system integration if available
            if ENHANCED_LEARNING_AVAILABLE:
                try:
                    # Force reimport of learning system integration modules
                    import importlib
                    import sys

                    # Reload all learning-related modules
                    modules_to_reload = [
                        'learning_system_integration',
                        'enhanced_adaptive_learning_v2',
                        'adaptive_learning_system',
                        'enhanced_adaptive_learning_system'
                    ]

                    for module_name in modules_to_reload:
                        if module_name in sys.modules:
                            try:
                                importlib.reload(sys.modules[module_name])
                                print(f"✅ Reloaded {module_name}")
                            except Exception as e:
                                print(f"⚠️ Could not reload {module_name}: {e}")

                    # Update the global integration reference
                    global enhanced_gui_integration, learning_integrator
                    from learning_system_integration import enhanced_gui_integration, learning_integrator

                    print("✅ Learning system integration reinitialized")
                except Exception as e:
                    print(f"⚠️ Warning: Could not reinitialize learning integration: {e}")

            # Reinitialize betting system if it exists
            if hasattr(self, 'betting_system') and self.betting_system:
                try:
                    self.betting_system.load_betting_data()
                    print("✅ Betting system reinitialized")
                except Exception as e:
                    print(f"⚠️ Warning: Could not reinitialize betting system: {e}")

            # Clear any UI state that might reference old data
            self.clear_all()

            # Refresh statistics to ensure clean state
            try:
                self.refresh_statistics()
                self.update_quick_stats()
                print("✅ Statistics refreshed")
            except Exception as e:
                print(f"⚠️ Warning: Could not refresh statistics: {e}")

            print("✅ Learning systems reinitialized successfully")

        except Exception as e:
            print(f"❌ Error during learning system reinitialization: {e}")
            raise

    def _force_close_dialog(self, dialog):
        """Force close a dialog that might be stuck"""
        try:
            print("⚠️ Force closing stuck dialog...")
            dialog.close()
            dialog.deleteLater()

            # Force multiple event processing cycles
            for _ in range(10):
                QApplication.processEvents()

            # Try to hide all modal dialogs
            for widget in QApplication.topLevelWidgets():
                if isinstance(widget, (QMessageBox, QDialog)) and widget.isModal():
                    try:
                        widget.close()
                        widget.deleteLater()
                    except:
                        pass

            print("✅ Dialog force closed")
        except Exception as e:
            print(f"❌ Could not force close dialog: {e}")

    def _aggressive_dialog_close(self, dialog):
        """Aggressively close a dialog using multiple methods"""
        try:
            print("🔄 Aggressively closing dialog...")

            # Method 1: Standard close
            if dialog and not dialog.isHidden():
                dialog.close()

            # Method 2: Hide the dialog
            if dialog:
                dialog.hide()

            # Method 3: Set result and accept
            if hasattr(dialog, 'accept'):
                dialog.accept()

            # Method 4: Delete the dialog
            if dialog:
                dialog.deleteLater()

            # Method 5: Process events multiple times
            for _ in range(20):
                QApplication.processEvents()

            # Method 6: Close all modal dialogs
            for widget in QApplication.topLevelWidgets():
                if widget.isModal() and widget.isVisible():
                    try:
                        widget.close()
                        widget.hide()
                        widget.deleteLater()
                    except:
                        pass

            print("✅ Dialog aggressively closed")

        except Exception as e:
            print(f"❌ Aggressive dialog close failed: {e}")

def main():
    app = QApplication(sys.argv)
    window = EnhancedTennisApp()
    window.show()
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
